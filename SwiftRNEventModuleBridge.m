//
//  SwiftRNEventModuleBridge.m
//  tingLite
//
//  Created by x<PERSON>od<PERSON> on 2025/1/14.
//  Copyright © 2025 ximalaya. All rights reserved.
//

#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>

// 为 Swift 事件模块创建 Objective-C 桥接
@interface RCT_EXTERN_MODULE(SwiftEventModule, RCTEventEmitter)

// 导出开始网络监听方法
RCT_EXTERN_METHOD(startNetworkMonitoring)

// 导出发送自定义事件方法
RCT_EXTERN_METHOD(sendCustomEvent:(NSDictionary *)params)

// 导出获取当前位置方法
RCT_EXTERN_METHOD(getCurrentLocation:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject)

@end
