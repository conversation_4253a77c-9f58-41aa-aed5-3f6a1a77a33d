//
//  SwiftRNModuleExample.swift
//  tingLite
//
//  Created by xiaodong on 2025/1/14.
//  Copyright © 2025 ximalaya. All rights reserved.
//

import Foundation
import React

/// Swift 实现的 React Native 模块示例
@objc(SwiftBusinessModule)
class SwiftBusinessModule: NSObject {
    
    // MARK: - RCTBridgeModule 协议实现
    
    /// 模块名称，RN 侧通过这个名称调用
    @objc static func moduleName() -> String {
        return "SwiftBusiness"
    }
    
    /// 指定方法执行的队列，默认为主队列
    @objc static func requiresMainQueueSetup() -> <PERSON><PERSON> {
        return false
    }
    
    /// 返回方法执行的队列
    @objc func methodQueue() -> DispatchQueue {
        return DispatchQueue.global(qos: .default)
    }
    
    // MARK: - 导出方法
    
    /// 上传视频方法
    @objc func uploadVideo(_ params: [String: Any],
                          resolve: @escaping RCTPromiseResolveBlock,
                          reject: @escaping RCTPromiseRejectBlock) {
        
        guard let filePath = params["filePath"] as? String,
              !filePath.isEmpty,
              FileManager.default.fileExists(atPath: filePath) else {
            reject("-1", "视频文件不存在", nil)
            return
        }
        
        let ext = params["ext"] as? String ?? URL(fileURLWithPath: filePath).pathExtension
        let uploadType = params["uploadType"] as? String ?? "video"
        
        // 模拟上传过程
        DispatchQueue.global().async {
            // 这里应该是实际的上传逻辑
            Thread.sleep(forTimeInterval: 2.0) // 模拟网络请求
            
            DispatchQueue.main.async {
                // 模拟成功返回
                let result = [
                    "fileId": "12345",
                    "fileUrl": "https://example.com/uploaded/\(UUID().uuidString).\(ext)",
                    "uploadType": uploadType
                ]
                resolve(result)
            }
        }
    }
    
    /// 获取设备信息
    @objc func getDeviceInfo(_ resolve: @escaping RCTPromiseResolveBlock,
                            reject: @escaping RCTPromiseRejectBlock) {
        
        let deviceInfo: [String: Any] = [
            "model": UIDevice.current.model,
            "systemName": UIDevice.current.systemName,
            "systemVersion": UIDevice.current.systemVersion,
            "name": UIDevice.current.name,
            "identifierForVendor": UIDevice.current.identifierForVendor?.uuidString ?? "",
            "timestamp": Date().timeIntervalSince1970
        ]
        
        resolve(deviceInfo)
    }
    
    /// 检查应用是否安装
    @objc func isAppInstalled(_ params: [String: Any],
                             resolve: @escaping RCTPromiseResolveBlock,
                             reject: @escaping RCTPromiseRejectBlock) {
        
        guard let appInfoList = params["appInfoList"] as? [String] else {
            reject("-1", "appInfoList 参数异常", NSError(domain: "xm.rn.swift.business", code: -1, userInfo: ["msg": "appInfoList 参数异常"]))
            return
        }
        
        let results = appInfoList.map { urlString -> Int in
            guard let url = URL(string: urlString),
                  UIApplication.shared.canOpenURL(url) else {
                return 0
            }
            return 1
        }
        
        resolve(["result": results])
    }
    
    /// 显示原生弹窗
    @objc func showNativeAlert(_ params: [String: Any],
                              resolve: @escaping RCTPromiseResolveBlock,
                              reject: @escaping RCTPromiseRejectBlock) {
        
        let title = params["title"] as? String ?? "提示"
        let message = params["message"] as? String ?? ""
        let buttonTitle = params["buttonTitle"] as? String ?? "确定"
        
        DispatchQueue.main.async {
            guard let topViewController = self.topViewController() else {
                reject("-1", "无法获取当前视图控制器", nil)
                return
            }
            
            let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: buttonTitle, style: .default) { _ in
                resolve(["action": "confirmed"])
            })
            
            topViewController.present(alert, animated: true)
        }
    }
    
    // MARK: - 私有方法
    
    private func topViewController() -> UIViewController? {
        guard let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }),
              let rootViewController = window.rootViewController else {
            return nil
        }
        
        return topViewController(from: rootViewController)
    }
    
    private func topViewController(from viewController: UIViewController) -> UIViewController {
        if let presentedViewController = viewController.presentedViewController {
            return topViewController(from: presentedViewController)
        }
        
        if let navigationController = viewController as? UINavigationController,
           let topViewController = navigationController.topViewController {
            return topViewController(from: topViewController)
        }
        
        if let tabBarController = viewController as? UITabBarController,
           let selectedViewController = tabBarController.selectedViewController {
            return topViewController(from: selectedViewController)
        }
        
        return viewController
    }
}

// MARK: - RCTBridgeModule 协议实现
extension SwiftBusinessModule: RCTBridgeModule {
    
    /// 返回常量，这些常量会在模块初始化时传递给 RN 侧
    @objc func constantsToExport() -> [String: Any] {
        return [
            "PLATFORM": "iOS",
            "VERSION": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
            "BUILD": Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
        ]
    }
}
