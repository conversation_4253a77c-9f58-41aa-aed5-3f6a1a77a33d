/**
 * Swift 桥接测试文件
 * 用于验证 Business.listenEarnRewardCoinNew 的 Swift 桥接是否成功
 */

import { NativeModules } from 'react-native';

const { Business } = NativeModules;

/**
 * 测试 Swift 桥接功能
 */
export const testSwiftBridge = async () => {
  console.log('🚀 开始测试 Swift 桥接...');
  
  // 检查模块是否存在
  if (!Business) {
    console.error('❌ Business 模块不存在');
    return false;
  }
  
  console.log('✅ Business 模块存在');
  
  // 检查方法是否存在
  if (!Business.listenEarnRewardCoinNew) {
    console.error('❌ listenEarnRewardCoinNew 方法不存在');
    return false;
  }
  
  console.log('✅ listenEarnRewardCoinNew 方法存在');
  
  // 测试调用
  try {
    const testParams = {
      rewardVideoStyle: 1,
      positionName: 'test_position',
      testFlag: true
    };
    
    console.log('📤 发送测试参数:', testParams);
    
    const result = await Business.listenEarnRewardCoinNew(testParams);
    
    console.log('📥 收到返回结果:', result);
    
    // 验证返回结果
    if (result && result.success === true) {
      console.log('🎉 Swift 桥接测试成功！');
      console.log('📊 测试结果详情:');
      console.log('   - 消息:', result.message);
      console.log('   - 样式:', result.rewardVideoStyleNew);
      console.log('   - 位置:', result.positionName);
      console.log('   - 时间戳:', new Date(result.timestamp * 1000).toLocaleString());
      return true;
    } else {
      console.error('❌ 返回结果格式不正确:', result);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 调用失败:', error);
    console.error('错误详情:', {
      code: error.code,
      message: error.message,
      domain: error.domain
    });
    return false;
  }
};

/**
 * 测试常量导出
 */
export const testConstants = () => {
  console.log('🔧 测试常量导出...');
  
  if (Business.getConstants) {
    const constants = Business.getConstants();
    console.log('📋 导出的常量:', constants);
    
    if (constants && constants.BRIDGE_TEST) {
      console.log('✅ 常量导出测试成功:', constants.BRIDGE_TEST);
      return true;
    }
  }
  
  console.log('⚠️ 常量导出功能不可用或未找到测试标记');
  return false;
};

/**
 * 完整的桥接测试
 */
export const runFullBridgeTest = async () => {
  console.log('🧪 开始完整的 Swift 桥接测试...');
  console.log('='.repeat(50));
  
  // 测试常量
  const constantsResult = testConstants();
  
  console.log('-'.repeat(30));
  
  // 测试方法调用
  const methodResult = await testSwiftBridge();
  
  console.log('='.repeat(50));
  console.log('📊 测试总结:');
  console.log(`   - 常量导出: ${constantsResult ? '✅ 成功' : '❌ 失败'}`);
  console.log(`   - 方法调用: ${methodResult ? '✅ 成功' : '❌ 失败'}`);
  
  const overallSuccess = constantsResult && methodResult;
  console.log(`   - 整体结果: ${overallSuccess ? '🎉 全部成功' : '⚠️ 部分失败'}`);
  
  return overallSuccess;
};

// 导出测试函数
export default {
  testSwiftBridge,
  testConstants,
  runFullBridgeTest
};
