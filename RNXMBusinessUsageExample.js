/**
 * RNXMBusiness Swift 模块使用示例
 * 
 * 这个文件展示了如何在 React Native 侧调用 Swift 实现的 listenEarnRewardCoinNew 方法
 */

import { NativeModules } from 'react-native';

const { RNXMBusiness } = NativeModules;

/**
 * 监听赚取奖励金币（新版）
 * @param {Object} params - 参数对象
 * @param {number} params.rewardVideoStyle - 奖励视频样式：0-旧版，1-新版
 * @param {string} params.positionName - 位置名称，默认为 "integral_center_inspire_video"
 * @returns {Promise} 返回奖励结果
 */
export const listenEarnRewardCoinNew = async (params = {}) => {
  try {
    const defaultParams = {
      rewardVideoStyle: 0, // 默认使用旧版样式
      positionName: 'integral_center_inspire_video', // 默认位置名称
      ...params // 合并用户传入的参数
    };

    console.log('调用 listenEarnRewardCoinNew，参数:', defaultParams);
    
    const result = await RNXMBusiness.listenEarnRewardCoinNew(defaultParams);
    
    console.log('listenEarnRewardCoinNew 成功:', result);
    return result;
    
  } catch (error) {
    console.error('listenEarnRewardCoinNew 失败:', error);
    throw error;
  }
};

/**
 * 使用示例
 */
export const exampleUsage = () => {
  
  // 示例1: 使用默认参数
  listenEarnRewardCoinNew()
    .then(result => {
      console.log('获得奖励:', result);
      // 处理成功结果
      // result 可能包含：
      // - rewardVideoStyleNew: boolean
      // - positionName: string
      // - adInfo: object (广告信息)
      // - 其他奖励相关数据
    })
    .catch(error => {
      console.error('获取奖励失败:', error);
      // 处理错误
    });

  // 示例2: 使用新版奖励视频样式
  listenEarnRewardCoinNew({
    rewardVideoStyle: 1, // 使用新版样式
    positionName: 'custom_position_name'
  })
    .then(result => {
      console.log('新版样式奖励结果:', result);
    })
    .catch(error => {
      console.error('新版样式获取失败:', error);
    });

  // 示例3: 使用 async/await 语法
  const handleRewardVideo = async () => {
    try {
      const result = await listenEarnRewardCoinNew({
        rewardVideoStyle: 1,
        positionName: 'integral_center_inspire_video'
      });
      
      // 检查是否成功获得奖励
      if (result && result.adInfo) {
        console.log('广告信息:', result.adInfo);
        console.log('使用新版样式:', result.rewardVideoStyleNew);
        console.log('位置名称:', result.positionName);
        
        // 这里可以更新 UI，显示奖励结果
        // 例如：显示金币增加动画、更新用户余额等
      }
      
    } catch (error) {
      // 处理各种错误情况
      if (error.code === 'REWARD_FAILED') {
        console.log('未能获取奖励金币');
        // 显示相应的提示信息
      } else {
        console.error('其他错误:', error);
      }
    }
  };
};

/**
 * 获取模块常量
 */
export const getModuleConstants = () => {
  if (RNXMBusiness && RNXMBusiness.getConstants) {
    const constants = RNXMBusiness.getConstants();
    console.log('模块常量:', constants);
    // 常量可能包含：
    // - DEFAULT_POSITION_NAME: "integral_center_inspire_video"
    // - REWARD_VIDEO_STYLE_OLD: 0
    // - REWARD_VIDEO_STYLE_NEW: 1
    return constants;
  }
  return null;
};

// 导出默认函数
export default {
  listenEarnRewardCoinNew,
  exampleUsage,
  getModuleConstants
};
