//
//  XMLRNManager.swift
//  tingLite
//
//  Created by x<PERSON>od<PERSON> on 2025/1/14.
//  Copyright © 2025 ximalaya. All rights reserved.
//

import Foundation
import UIKit
import XMRNManager
import XMConfigModule
import XMAccount
import LoginModule

/// Swift singleton wrapper for XMRNManager that implements XMRNManagerProtocol
@objc public class XMLRNManager: NSObject {

    // MARK: - Singleton
    @objc public static let shared = XMLRNManager()

    private override init() {
        super.init()
        // Set self as delegate to the underlying XMRNManager
        XMRNManager.sharedInstance().delegate = self
    }

    // MARK: - Public Methods

    @objc public func initWithAppId(_ appId: String,
                                   userId: String?,
                                   deviceId: String,
                                   bundleId: String?) {
                                    
        XMRNManager.sharedInstance().initWithAppId(appId,
                                                  andUserId: userId,
                                                  andDeviceId: deviceId,
                                                  andBundleId: bundleId)
    }

    @objc public func startWithUrl(_ bundleUrl: String,
                                  from sourceViewController: UIViewController?,
                                  withParameters parameters: [String: Any]?,
                                  goBackHandler: @escaping (Bool, Any?, Error?) -> Void) {
        XMRNManager.sharedInstance().start(withUrl: bundleUrl,
                                          from: sourceViewController,
                                          withParameters: parameters,
                                          goBackHandler: goBackHandler)
    }

    /// Notify RN side of user login
    @objc public func notifyUserLogin() {
        XMRNManager.sharedInstance().notifyUserLogin()
    }

    /// Notify RN side of user logout
    @objc public func notifyUserLogout() {
        XMRNManager.sharedInstance().notifyUserLogout()
    }

    // MARK: - Properties

    /// User ID
    @objc public var userId: String? {
        return XMRNManager.sharedInstance().userId
    }

    /// Device ID
    @objc public var deviceId: String {
        return XMRNManager.sharedInstance().deviceId
    }

    /// Bundle ID
    @objc public var bundleId: String? {
        return XMRNManager.sharedInstance().bundleId
    }
}

// MARK: - XMRNManagerProtocol Implementation
extension XMLRNManager: XMRNManagerProtocol {

    /// Get header cookie string
    @objc public func headerCookie() -> String {
        var cookie = XMConfig.shared().reqHeaderCookie()
        cookie.addXMCookie(appid  , key: "appid")
        return cookie
    }

    /// Get user information dictionary
    @objc public func getUserInfo() -> [AnyHashable : Any] {
        // 使用项目中的账户接口获取用户信息
        let settings = XMSettings.shared()
        let userModel = settings.userModel
        let userInstance = XMUserInstance.share()

        // 获取登录状态
        let isLogin = userInstance.isLogin

        // 构建用户信息字典
        var result: [AnyHashable: Any] = [:]

        if isLogin, let user = userModel.user {
            // 用户已登录，获取真实用户信息
            result["isLogin"] = true
            result["uid"] = user.uid
            result["token"] = user.token ?? ""
            result["nickName"] = user.nickname ?? ""
            result["imgUrl"] = user.smallLogo?.absoluteString ?? ""
            result["phone"] = user.phone ?? ""
            result["mobileLoginable"] = userModel.mobileLoginable
            result["isVip"] = userModel.vip.isVip
        } else {
            // 用户未登录，返回默认值
            result["isLogin"] = false
            result["uid"] = 0
            result["token"] = ""
            result["nickName"] = ""
            result["imgUrl"] = ""
            result["hasSetPwd"] = false
            result["phone"] = ""
            result["mobileLoginable"] = true
            result["isVip"] = false
        }

        return result
    }

    /// Handle user logout
    @objc public func logout() {
    }
}
