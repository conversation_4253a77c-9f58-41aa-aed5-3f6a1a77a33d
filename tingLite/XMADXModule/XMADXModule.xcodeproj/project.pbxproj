// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		225A31FD26FB0588008009DF /* XMLDRShakeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225A31FC26FB0588008009DF /* XMLDRShakeView.swift */; };
		22FC4DCB26E5DC1C000B2199 /* XMLDRVideoFinishedFrameAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22FC4DC726E5DC1B000B2199 /* XMLDRVideoFinishedFrameAlert.swift */; };
		22FC4DCC26E5DC1C000B2199 /* XMLDRVideoPlayerViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22FC4DC826E5DC1C000B2199 /* XMLDRVideoPlayerViewController.swift */; };
		22FC4DCE26E5DC1C000B2199 /* XMLDRBottomCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22FC4DCA26E5DC1C000B2199 /* XMLDRBottomCardView.swift */; };
		22FC4DD026E62C00000B2199 /* XMLDRRewardVideoAd.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22FC4DCF26E62BFF000B2199 /* XMLDRRewardVideoAd.swift */; };
		3AAC1FBB06B955386AB5C8EA /* Pods_XMADXModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AE0AC646AD8E66464DB853D0 /* Pods_XMADXModule.framework */; };
		7620DB7990B5C69F63CF6C73 /* Pods_XMADXModuleTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D10311DF1944CAB200B07C32 /* Pods_XMADXModuleTests.framework */; };
		9504F66D252455CA00C76721 /* XMLADXSoundManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9504F66C252455C900C76721 /* XMLADXSoundManager.swift */; };
		9506FCCA26809AE6007A46FE /* XMLADXNotification.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9506FCC926809AE6007A46FE /* XMLADXNotification.swift */; };
		95079435249084C100E054D0 /* XMLBDNativeAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95079434249084C100E054D0 /* XMLBDNativeAdTrigger.swift */; };
		950794372491DBBE00E054D0 /* XMLBDRewardedVideoAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 950794362491DBBE00E054D0 /* XMLBDRewardedVideoAdTrigger.swift */; };
		950819FB26E658B600C60D40 /* XMLDRRewardedVideoAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 950819FA26E658B600C60D40 /* XMLDRRewardedVideoAdTrigger.swift */; };
		950A5B742600D85000972866 /* XMLADXProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 950A5B732600D85000972866 /* XMLADXProtocol.swift */; };
		952C99E8269699ED00865B68 /* XMLDRVideoPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 952C99E7269699ED00865B68 /* XMLDRVideoPlayer.swift */; };
		952C99EC2696D7E100865B68 /* XMLDRSplashView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 952C99EB2696D7E100865B68 /* XMLDRSplashView.swift */; };
		952C99F22696E5B900865B68 /* XMLDRReletedAdView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 952C99F12696E5B900865B68 /* XMLDRReletedAdView.swift */; };
		952C9A1F2697150500865B68 /* XMLDRNativeEnum.swift in Sources */ = {isa = PBXBuildFile; fileRef = 952C9A1E2697150500865B68 /* XMLDRNativeEnum.swift */; };
		952C9A25269B2BBD00865B68 /* XMLDRVideoManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 952C9A24269B2BBD00865B68 /* XMLDRVideoManager.swift */; };
		952F1CE5247F65AA0093B01D /* XMADXModule.h in Headers */ = {isa = PBXBuildFile; fileRef = 952F1CE3247F65AA0093B01D /* XMADXModule.h */; settings = {ATTRIBUTES = (Public, ); }; };
		953A2FD2247FAB9F002FFA66 /* XMLDRSplashViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FBA247FAB9E002FFA66 /* XMLDRSplashViewController.swift */; };
		953A2FD3247FAB9F002FFA66 /* XMLDRMediaView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FBB247FAB9E002FFA66 /* XMLDRMediaView.swift */; };
		953A2FD4247FAB9F002FFA66 /* XMLBUNativeAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FBC247FAB9E002FFA66 /* XMLBUNativeAdTrigger.swift */; };
		953A2FD5247FAB9F002FFA66 /* XMLDRNativeAd.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FBD247FAB9E002FFA66 /* XMLDRNativeAd.swift */; };
		953A2FD6247FAB9F002FFA66 /* XMLBUSplashViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FBE247FAB9E002FFA66 /* XMLBUSplashViewController.swift */; };
		953A2FD7247FAB9F002FFA66 /* XMLGDTNativeAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FBF247FAB9E002FFA66 /* XMLGDTNativeAdTrigger.swift */; };
		953A2FD8247FAB9F002FFA66 /* XMLDRNativeAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FC0247FAB9E002FFA66 /* XMLDRNativeAdTrigger.swift */; };
		953A2FD9247FAB9F002FFA66 /* XMLGDTSplashViewTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FC1247FAB9E002FFA66 /* XMLGDTSplashViewTrigger.swift */; };
		953A2FDA247FAB9F002FFA66 /* XMLBURewardedVideoAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FC2247FAB9E002FFA66 /* XMLBURewardedVideoAdTrigger.swift */; };
		953A2FDB247FAB9F002FFA66 /* XMLGDTRewardedVideoAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FC3247FAB9E002FFA66 /* XMLGDTRewardedVideoAdTrigger.swift */; };
		953A2FDC247FAB9F002FFA66 /* XMLADXLoaderDRTransfer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FC5247FAB9E002FFA66 /* XMLADXLoaderDRTransfer.swift */; };
		953A2FDD247FAB9F002FFA66 /* XMLADXManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FC6247FAB9E002FFA66 /* XMLADXManager.swift */; };
		953A2FDE247FAB9F002FFA66 /* XMLADXLoaderBUTransfer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FC7247FAB9E002FFA66 /* XMLADXLoaderBUTransfer.swift */; };
		953A2FDF247FAB9F002FFA66 /* XMLADXLoaderManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FC8247FAB9E002FFA66 /* XMLADXLoaderManager.swift */; };
		953A2FE0247FAB9F002FFA66 /* XMLADXLoaderItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FC9247FAB9E002FFA66 /* XMLADXLoaderItem.swift */; };
		953A2FE1247FAB9F002FFA66 /* XMLADXReportManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FCA247FAB9E002FFA66 /* XMLADXReportManager.swift */; };
		953A2FE2247FAB9F002FFA66 /* XMLADXLoaderGDTTransfer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FCB247FAB9E002FFA66 /* XMLADXLoaderGDTTransfer.swift */; };
		953A2FE3247FAB9F002FFA66 /* XMLADXMacro.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FCC247FAB9E002FFA66 /* XMLADXMacro.swift */; };
		953A2FE4247FAB9F002FFA66 /* XMLADXLoader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FCD247FAB9E002FFA66 /* XMLADXLoader.swift */; };
		953A2FE5247FAB9F002FFA66 /* XMLADXUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FCE247FAB9E002FFA66 /* XMLADXUtils.swift */; };
		953A2FE7247FAB9F002FFA66 /* XMLAdRequestManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953A2FD1247FAB9F002FFA66 /* XMLAdRequestManager.swift */; };
		953A2FF3247FAC7E002FFA66 /* XMUtilModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 953A2FF2247FAC7E002FFA66 /* XMUtilModule.framework */; };
		953A2FF5247FAC86002FFA66 /* RouterModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 953A2FF4247FAC86002FFA66 /* RouterModule.framework */; };
		953A2FF7247FAC91002FFA66 /* XMConfigModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 953A2FF6247FAC91002FFA66 /* XMConfigModule.framework */; };
		953A2FF9247FAC9A002FFA66 /* XMNetworkModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 953A2FF8247FAC9A002FFA66 /* XMNetworkModule.framework */; };
		953A2FFB247FACA1002FFA66 /* BaseModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 953A2FFA247FACA1002FFA66 /* BaseModule.framework */; };
		9540350124A1D0E40005316A /* XMLADXEnum.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9540350024A1D0E40005316A /* XMLADXEnum.swift */; };
		95452B5725357499007DFA68 /* XMADXMedia.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 95D7A4FC2535518800E3A67A /* XMADXMedia.bundle */; };
		9556BD9B259B209A0099963B /* XMLADXErrorCodeManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9556BD9A259B20990099963B /* XMLADXErrorCodeManager.swift */; };
		95654AAC2678B05600717A02 /* XMLVideoADFreeTopMask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95654AAB2678B05600717A02 /* XMLVideoADFreeTopMask.swift */; };
		956CB790264CCFE7004AC7BA /* XMLNativeAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 956CB78F264CCFE7004AC7BA /* XMLNativeAdTrigger.swift */; };
		957333432484EB8200F105C0 /* XMADXModuleTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 957333422484EB8200F105C0 /* XMADXModuleTests.swift */; };
		957333452484EB8200F105C0 /* XMADXModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 952F1CE0247F65AA0093B01D /* XMADXModule.framework */; };
		95756F6024E115FF0089B6A0 /* XMLADXPretreater.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95756F5F24E115FE0089B6A0 /* XMLADXPretreater.swift */; };
		95756F6624E11A780089B6A0 /* XMLAdRequestData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95756F6524E11A780089B6A0 /* XMLAdRequestData.swift */; };
		95756F6824E11C270089B6A0 /* XMLAdRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95756F6724E11C270089B6A0 /* XMLAdRequest.swift */; };
		9579A4A024DD1F240021751B /* XMLADXCacheManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9579A49F24DD1F240021751B /* XMLADXCacheManager.swift */; };
		9579A4B724DD301B0021751B /* XMLADXBaseLoaderManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9579A4B624DD301B0021751B /* XMLADXBaseLoaderManager.swift */; };
		9579A4B924DD36140021751B /* XMLADXPreLoaderManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9579A4B824DD36130021751B /* XMLADXPreLoaderManager.swift */; };
		9579A4BB24DD3D130021751B /* XMLADXPreDataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9579A4BA24DD3D130021751B /* XMLADXPreDataManager.swift */; };
		957D1BE42580A1CC0036ECC0 /* XMLADFreeMask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 957D1BE32580A1CC0036ECC0 /* XMLADFreeMask.swift */; };
		957D1BE62580E84E0036ECC0 /* XMLADXFreeManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 957D1BE52580E84E0036ECC0 /* XMLADXFreeManager.swift */; };
		958D243F24FCE80A00BB2DB9 /* XMLRewardedVideoAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 958D243E24FCE80900BB2DB9 /* XMLRewardedVideoAdTrigger.swift */; };
		959148E3255A3F780069AD38 /* XMLADXMediaManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959148E2255A3F780069AD38 /* XMLADXMediaManager.swift */; };
		959148E5255A3F8C0069AD38 /* XMLADXMediaStaff.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959148E4255A3F8C0069AD38 /* XMLADXMediaStaff.swift */; };
		95A2559224938ED3008B47E7 /* XMLBDNativeAdLogoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95A2559124938ED3008B47E7 /* XMLBDNativeAdLogoView.swift */; };
		95A77FAF26DDFEC40049299B /* XMLADXRTBManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95A77FAE26DDFEC40049299B /* XMLADXRTBManager.swift */; };
		95A88D6026A170A3001881D0 /* XMLVideoADClaimTopMask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95A88D5F26A170A3001881D0 /* XMLVideoADClaimTopMask.swift */; };
		95ACAC1B25417EF0002CA15F /* XMLDRSoundPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95ACAC1A25417EF0002CA15F /* XMLDRSoundPlayer.swift */; };
		95ACAC232542731A002CA15F /* XMLADXSoundStaff.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95ACAC222542731A002CA15F /* XMLADXSoundStaff.swift */; };
		95ACAC26254273B3002CA15F /* XMLADXSafetyGroup.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95ACAC25254273B3002CA15F /* XMLADXSafetyGroup.swift */; };
		95CBBC0524F6408400D5D05B /* XMLRewardedVideoToolBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95CBBC0424F6408400D5D05B /* XMLRewardedVideoToolBar.swift */; };
		95CBBC2024F7D0DE00D5D05B /* XMLRewardedVideoAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95CBBC1F24F7D0DE00D5D05B /* XMLRewardedVideoAlert.swift */; };
		95CBBC2224F8A38000D5D05B /* XMLPulAdRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95CBBC2124F8A37F00D5D05B /* XMLPulAdRequest.swift */; };
		95CBBC2424F8D48A00D5D05B /* XMLADXPulLoaderManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95CBBC2324F8D48900D5D05B /* XMLADXPulLoaderManager.swift */; };
		95CBBC2624F8F0DA00D5D05B /* XMLPulAdRequestManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95CBBC2524F8F0D900D5D05B /* XMLPulAdRequestManager.swift */; };
		95D4018A248F77FB0093F596 /* XMLADXLoaderBDTransfer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95D40189248F77FB0093F596 /* XMLADXLoaderBDTransfer.swift */; };
		95D4018C248F90060093F596 /* XMLBDSplashViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95D4018B248F90060093F596 /* XMLBDSplashViewController.swift */; };
		95D5A7C324985E8200257D6A /* XMLAdRequestUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95D5A7C224985E8200257D6A /* XMLAdRequestUtils.swift */; };
		95D7A5032535557800E3A67A /* XMADXAssets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 95CBBC1A24F7CD8A00D5D05B /* XMADXAssets.xcassets */; };
		95DCED262726D8B3006AE1CB /* XMLBUPlaqueAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DCED252726D8B3006AE1CB /* XMLBUPlaqueAdTrigger.swift */; };
		95DCED282726D8BF006AE1CB /* XMLGDTPlaqueAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DCED272726D8BF006AE1CB /* XMLGDTPlaqueAdTrigger.swift */; };
		95E1AF642488ED5E00E78CCE /* XMLADXConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E1AF632488ED5E00E78CCE /* XMLADXConfig.swift */; };
		95E1AF66248A18E800E78CCE /* XMLAdRequestSubModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E1AF65248A18E800E78CCE /* XMLAdRequestSubModels.swift */; };
		95E532CA25F6056300892209 /* XMLVideoADFullTopMask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E532C925F6056300892209 /* XMLVideoADFullTopMask.swift */; };
		95E5337325F730D900892209 /* XMLSplashJumpMaskWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E5337225F730D900892209 /* XMLSplashJumpMaskWidget.swift */; };
		95EDFC06276C331400463484 /* XMLPatchFlowerChannel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95EDFC05276C331400463484 /* XMLPatchFlowerChannel.swift */; };
		95EDFC08276C331E00463484 /* XMLPatchFlower.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95EDFC07276C331E00463484 /* XMLPatchFlower.swift */; };
		95EF6FF2276B637E00834441 /* XMLPatchScatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95EF6FF1276B637E00834441 /* XMLPatchScatter.swift */; };
		95F5D60024DD589D00A165CF /* XMLADXPreManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95F5D5FF24DD589D00A165CF /* XMLADXPreManager.swift */; };
		95F93AD9249A1AE40053B28C /* XMLADXQueueManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95F93AD8249A1AE40053B28C /* XMLADXQueueManager.swift */; };
		95F93ADB249A2FD20053B28C /* XMLADXWidgetManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95F93ADA249A2FD20053B28C /* XMLADXWidgetManager.swift */; };
		95FEFEA92609E74A0033F39A /* XMLRewardedVideoTBNormal.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95FEFEA82609E74A0033F39A /* XMLRewardedVideoTBNormal.swift */; };
		95FEFEAB2609E80C0033F39A /* XMLRewardedVideoTBVip.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95FEFEAA2609E80C0033F39A /* XMLRewardedVideoTBVip.swift */; };
		B61ADBCA2769C16C001743D1 /* XMLADXLoaderJDTransfer.swift in Sources */ = {isa = PBXBuildFile; fileRef = B61ADBC92769C16C001743D1 /* XMLADXLoaderJDTransfer.swift */; };
		B61B080627BE449200DE8017 /* XMLDRBroadcastADPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B61B080527BE449200DE8017 /* XMLDRBroadcastADPopupView.swift */; };
		B62EE5E7278FD04E00331D49 /* XMLDRNativePlaqueAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = B62EE5E6278FD04E00331D49 /* XMLDRNativePlaqueAdTrigger.swift */; };
		B64ADF1C27955488005ACE22 /* XMLBUNativePlaqueAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = B64ADF1B27955488005ACE22 /* XMLBUNativePlaqueAdTrigger.swift */; };
		B64ADF1E27957BA0005ACE22 /* XMLGDTNativePlaqueAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = B64ADF1D27957BA0005ACE22 /* XMLGDTNativePlaqueAdTrigger.swift */; };
		B64B565B279025D9007847F1 /* XMLPlaqueAdView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B64B565A279025D9007847F1 /* XMLPlaqueAdView.swift */; };
		B64D20212769D522002E4E9C /* XMLJDSplashViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B64D20202769D522002E4E9C /* XMLJDSplashViewController.swift */; };
		B64D2023276B1DE8002E4E9C /* XMLJDNativeAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = B64D2022276B1DE8002E4E9C /* XMLJDNativeAdTrigger.swift */; };
		B67E9CA6278D6D5000409D4E /* XMLSplashSlideMaskWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = B67E9CA5278D6D5000409D4E /* XMLSplashSlideMaskWidget.swift */; };
		B683418C26E231AE0047BBDE /* XMLADXCustomLoaderMgr.swift in Sources */ = {isa = PBXBuildFile; fileRef = B683418B26E231AE0047BBDE /* XMLADXCustomLoaderMgr.swift */; };
		B68ACB23278FE93900FEBC6A /* XMLNativePlaqueAdTrigger.swift in Sources */ = {isa = PBXBuildFile; fileRef = B68ACB22278FE93900FEBC6A /* XMLNativePlaqueAdTrigger.swift */; };
		B69715D62DF68D0C00A0CB27 /* XMLADXBiddingStrategy.swift in Sources */ = {isa = PBXBuildFile; fileRef = B69715D42DF68D0C00A0CB27 /* XMLADXBiddingStrategy.swift */; };
		B69715D82DF68D0C00A0CB27 /* XMLADXBiddingManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = B69715D32DF68D0C00A0CB27 /* XMLADXBiddingManager.swift */; };
		B69DEAF025021E7C00DB8380 /* MediaModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B69DEAEF25021E7C00DB8380 /* MediaModule.framework */; };
		CD43450D2E1386C40050E83C /* XMIAdABTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD43450C2E1386C20050E83C /* XMIAdABTest.swift */; };
		CD4345122E16711D0050E83C /* RNXMBusiness.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD4345112E16710A0050E83C /* RNXMBusiness.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		95452B5525357451007DFA68 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 952F1CD7247F65AA0093B01D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 95D7A4FB2535518800E3A67A;
			remoteInfo = XMADXMedia;
		};
		957333462484EB8200F105C0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 952F1CD7247F65AA0093B01D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 952F1CDF247F65AA0093B01D;
			remoteInfo = XMADXModule;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		225A31FC26FB0588008009DF /* XMLDRShakeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLDRShakeView.swift; sourceTree = "<group>"; };
		22FC4DC726E5DC1B000B2199 /* XMLDRVideoFinishedFrameAlert.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLDRVideoFinishedFrameAlert.swift; sourceTree = "<group>"; };
		22FC4DC826E5DC1C000B2199 /* XMLDRVideoPlayerViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLDRVideoPlayerViewController.swift; sourceTree = "<group>"; };
		22FC4DCA26E5DC1C000B2199 /* XMLDRBottomCardView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLDRBottomCardView.swift; sourceTree = "<group>"; };
		22FC4DCF26E62BFF000B2199 /* XMLDRRewardVideoAd.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLDRRewardVideoAd.swift; sourceTree = "<group>"; };
		3F4ED1FC0527017B8831A77D /* Pods-XMADXModuleTests.alpha.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XMADXModuleTests.alpha.xcconfig"; path = "Target Support Files/Pods-XMADXModuleTests/Pods-XMADXModuleTests.alpha.xcconfig"; sourceTree = "<group>"; };
		469BFFB0A492F684750DC983 /* Pods-XMADXModuleTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XMADXModuleTests.release.xcconfig"; path = "Target Support Files/Pods-XMADXModuleTests/Pods-XMADXModuleTests.release.xcconfig"; sourceTree = "<group>"; };
		9504F66C252455C900C76721 /* XMLADXSoundManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXSoundManager.swift; sourceTree = "<group>"; };
		9506FCC926809AE6007A46FE /* XMLADXNotification.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXNotification.swift; sourceTree = "<group>"; };
		95079434249084C100E054D0 /* XMLBDNativeAdTrigger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLBDNativeAdTrigger.swift; sourceTree = "<group>"; };
		950794362491DBBE00E054D0 /* XMLBDRewardedVideoAdTrigger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLBDRewardedVideoAdTrigger.swift; sourceTree = "<group>"; };
		950819FA26E658B600C60D40 /* XMLDRRewardedVideoAdTrigger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLDRRewardedVideoAdTrigger.swift; sourceTree = "<group>"; };
		950A5B732600D85000972866 /* XMLADXProtocol.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXProtocol.swift; sourceTree = "<group>"; };
		952C99E7269699ED00865B68 /* XMLDRVideoPlayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLDRVideoPlayer.swift; sourceTree = "<group>"; };
		952C99EB2696D7E100865B68 /* XMLDRSplashView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLDRSplashView.swift; sourceTree = "<group>"; };
		952C99F12696E5B900865B68 /* XMLDRReletedAdView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLDRReletedAdView.swift; sourceTree = "<group>"; };
		952C9A1E2697150500865B68 /* XMLDRNativeEnum.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLDRNativeEnum.swift; sourceTree = "<group>"; };
		952C9A24269B2BBD00865B68 /* XMLDRVideoManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLDRVideoManager.swift; sourceTree = "<group>"; };
		952F1CE0247F65AA0093B01D /* XMADXModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = XMADXModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		952F1CE3247F65AA0093B01D /* XMADXModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XMADXModule.h; sourceTree = "<group>"; };
		952F1CE4247F65AA0093B01D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		952F1D06247F82CC0093B01D /* XMConfigModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMConfigModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		952F1D08247F82F00093B01D /* RouterModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = RouterModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		952F1D0A247F832A0093B01D /* BaseModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = BaseModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		952F1D0C247F83E70093B01D /* HomeModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = HomeModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		952F1D0E247F83EE0093B01D /* MineModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = MineModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		952F1D10247F83F90093B01D /* MyListenModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = MyListenModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		952F1D12247F84020093B01D /* SearchModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = SearchModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		952F1D14247F840B0093B01D /* AudioModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = AudioModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		953A2FBA247FAB9E002FFA66 /* XMLDRSplashViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLDRSplashViewController.swift; sourceTree = "<group>"; };
		953A2FBB247FAB9E002FFA66 /* XMLDRMediaView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLDRMediaView.swift; sourceTree = "<group>"; };
		953A2FBC247FAB9E002FFA66 /* XMLBUNativeAdTrigger.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLBUNativeAdTrigger.swift; sourceTree = "<group>"; };
		953A2FBD247FAB9E002FFA66 /* XMLDRNativeAd.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLDRNativeAd.swift; sourceTree = "<group>"; };
		953A2FBE247FAB9E002FFA66 /* XMLBUSplashViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLBUSplashViewController.swift; sourceTree = "<group>"; };
		953A2FBF247FAB9E002FFA66 /* XMLGDTNativeAdTrigger.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLGDTNativeAdTrigger.swift; sourceTree = "<group>"; };
		953A2FC0247FAB9E002FFA66 /* XMLDRNativeAdTrigger.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLDRNativeAdTrigger.swift; sourceTree = "<group>"; };
		953A2FC1247FAB9E002FFA66 /* XMLGDTSplashViewTrigger.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLGDTSplashViewTrigger.swift; sourceTree = "<group>"; };
		953A2FC2247FAB9E002FFA66 /* XMLBURewardedVideoAdTrigger.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLBURewardedVideoAdTrigger.swift; sourceTree = "<group>"; };
		953A2FC3247FAB9E002FFA66 /* XMLGDTRewardedVideoAdTrigger.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLGDTRewardedVideoAdTrigger.swift; sourceTree = "<group>"; };
		953A2FC5247FAB9E002FFA66 /* XMLADXLoaderDRTransfer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLADXLoaderDRTransfer.swift; sourceTree = "<group>"; };
		953A2FC6247FAB9E002FFA66 /* XMLADXManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLADXManager.swift; sourceTree = "<group>"; };
		953A2FC7247FAB9E002FFA66 /* XMLADXLoaderBUTransfer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLADXLoaderBUTransfer.swift; sourceTree = "<group>"; };
		953A2FC8247FAB9E002FFA66 /* XMLADXLoaderManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLADXLoaderManager.swift; sourceTree = "<group>"; };
		953A2FC9247FAB9E002FFA66 /* XMLADXLoaderItem.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLADXLoaderItem.swift; sourceTree = "<group>"; };
		953A2FCA247FAB9E002FFA66 /* XMLADXReportManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLADXReportManager.swift; sourceTree = "<group>"; };
		953A2FCB247FAB9E002FFA66 /* XMLADXLoaderGDTTransfer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLADXLoaderGDTTransfer.swift; sourceTree = "<group>"; };
		953A2FCC247FAB9E002FFA66 /* XMLADXMacro.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLADXMacro.swift; sourceTree = "<group>"; };
		953A2FCD247FAB9E002FFA66 /* XMLADXLoader.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLADXLoader.swift; sourceTree = "<group>"; };
		953A2FCE247FAB9E002FFA66 /* XMLADXUtils.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLADXUtils.swift; sourceTree = "<group>"; };
		953A2FD1247FAB9F002FFA66 /* XMLAdRequestManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLAdRequestManager.swift; sourceTree = "<group>"; };
		953A2FE8247FAC2C002FFA66 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/MacOSX.platform/Developer/SDKs/MacOSX10.15.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		953A2FEA247FAC33002FFA66 /* XMAVKit.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMAVKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		953A2FEC247FAC3B002FFA66 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = Platforms/MacOSX.platform/Developer/SDKs/MacOSX10.15.sdk/System/Library/Frameworks/AVFoundation.framework; sourceTree = DEVELOPER_DIR; };
		953A2FEE247FAC4D002FFA66 /* AVKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVKit.framework; path = Platforms/MacOSX.platform/Developer/SDKs/MacOSX10.15.sdk/System/Library/Frameworks/AVKit.framework; sourceTree = DEVELOPER_DIR; };
		953A2FF0247FAC69002FFA66 /* XMADXSupportModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMADXSupportModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		953A2FF2247FAC7E002FFA66 /* XMUtilModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMUtilModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		953A2FF4247FAC86002FFA66 /* RouterModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = RouterModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		953A2FF6247FAC91002FFA66 /* XMConfigModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMConfigModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		953A2FF8247FAC9A002FFA66 /* XMNetworkModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMNetworkModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		953A2FFA247FACA1002FFA66 /* BaseModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = BaseModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		953A2FFC247FAE06002FFA66 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		9540350024A1D0E40005316A /* XMLADXEnum.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXEnum.swift; sourceTree = "<group>"; };
		9556BD9A259B20990099963B /* XMLADXErrorCodeManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXErrorCodeManager.swift; sourceTree = "<group>"; };
		95654AAB2678B05600717A02 /* XMLVideoADFreeTopMask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLVideoADFreeTopMask.swift; sourceTree = "<group>"; };
		956CB78F264CCFE7004AC7BA /* XMLNativeAdTrigger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLNativeAdTrigger.swift; sourceTree = "<group>"; };
		957333402484EB8200F105C0 /* XMADXModuleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = XMADXModuleTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		957333422484EB8200F105C0 /* XMADXModuleTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMADXModuleTests.swift; sourceTree = "<group>"; };
		957333442484EB8200F105C0 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		95756F5F24E115FE0089B6A0 /* XMLADXPretreater.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXPretreater.swift; sourceTree = "<group>"; };
		95756F6524E11A780089B6A0 /* XMLAdRequestData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLAdRequestData.swift; sourceTree = "<group>"; };
		95756F6724E11C270089B6A0 /* XMLAdRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLAdRequest.swift; sourceTree = "<group>"; };
		9579A49F24DD1F240021751B /* XMLADXCacheManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXCacheManager.swift; sourceTree = "<group>"; };
		9579A4B624DD301B0021751B /* XMLADXBaseLoaderManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXBaseLoaderManager.swift; sourceTree = "<group>"; };
		9579A4B824DD36130021751B /* XMLADXPreLoaderManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXPreLoaderManager.swift; sourceTree = "<group>"; };
		9579A4BA24DD3D130021751B /* XMLADXPreDataManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXPreDataManager.swift; sourceTree = "<group>"; };
		957D1BE32580A1CC0036ECC0 /* XMLADFreeMask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADFreeMask.swift; sourceTree = "<group>"; };
		957D1BE52580E84E0036ECC0 /* XMLADXFreeManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXFreeManager.swift; sourceTree = "<group>"; };
		958D243E24FCE80900BB2DB9 /* XMLRewardedVideoAdTrigger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLRewardedVideoAdTrigger.swift; sourceTree = "<group>"; };
		959148E2255A3F780069AD38 /* XMLADXMediaManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXMediaManager.swift; sourceTree = "<group>"; };
		959148E4255A3F8C0069AD38 /* XMLADXMediaStaff.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXMediaStaff.swift; sourceTree = "<group>"; };
		95A2559124938ED3008B47E7 /* XMLBDNativeAdLogoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLBDNativeAdLogoView.swift; sourceTree = "<group>"; };
		95A77FAE26DDFEC40049299B /* XMLADXRTBManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXRTBManager.swift; sourceTree = "<group>"; };
		95A88D5F26A170A3001881D0 /* XMLVideoADClaimTopMask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLVideoADClaimTopMask.swift; sourceTree = "<group>"; };
		95ACAC1A25417EF0002CA15F /* XMLDRSoundPlayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLDRSoundPlayer.swift; sourceTree = "<group>"; };
		95ACAC222542731A002CA15F /* XMLADXSoundStaff.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXSoundStaff.swift; sourceTree = "<group>"; };
		95ACAC25254273B3002CA15F /* XMLADXSafetyGroup.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXSafetyGroup.swift; sourceTree = "<group>"; };
		95CBBC0424F6408400D5D05B /* XMLRewardedVideoToolBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLRewardedVideoToolBar.swift; sourceTree = "<group>"; };
		95CBBC1A24F7CD8A00D5D05B /* XMADXAssets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = XMADXAssets.xcassets; sourceTree = "<group>"; };
		95CBBC1F24F7D0DE00D5D05B /* XMLRewardedVideoAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLRewardedVideoAlert.swift; sourceTree = "<group>"; };
		95CBBC2124F8A37F00D5D05B /* XMLPulAdRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPulAdRequest.swift; sourceTree = "<group>"; };
		95CBBC2324F8D48900D5D05B /* XMLADXPulLoaderManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXPulLoaderManager.swift; sourceTree = "<group>"; };
		95CBBC2524F8F0D900D5D05B /* XMLPulAdRequestManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPulAdRequestManager.swift; sourceTree = "<group>"; };
		95D40189248F77FB0093F596 /* XMLADXLoaderBDTransfer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXLoaderBDTransfer.swift; sourceTree = "<group>"; };
		95D4018B248F90060093F596 /* XMLBDSplashViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLBDSplashViewController.swift; sourceTree = "<group>"; };
		95D5A7C224985E8200257D6A /* XMLAdRequestUtils.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLAdRequestUtils.swift; sourceTree = "<group>"; };
		95D7A4FC2535518800E3A67A /* XMADXMedia.bundle */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = XMADXMedia.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		95D7A4FE2535518800E3A67A /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		95DCED252726D8B3006AE1CB /* XMLBUPlaqueAdTrigger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLBUPlaqueAdTrigger.swift; sourceTree = "<group>"; };
		95DCED272726D8BF006AE1CB /* XMLGDTPlaqueAdTrigger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLGDTPlaqueAdTrigger.swift; sourceTree = "<group>"; };
		95E1AF632488ED5E00E78CCE /* XMLADXConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXConfig.swift; sourceTree = "<group>"; };
		95E1AF65248A18E800E78CCE /* XMLAdRequestSubModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLAdRequestSubModels.swift; sourceTree = "<group>"; };
		95E532C925F6056300892209 /* XMLVideoADFullTopMask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLVideoADFullTopMask.swift; sourceTree = "<group>"; };
		95E5337225F730D900892209 /* XMLSplashJumpMaskWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLSplashJumpMaskWidget.swift; sourceTree = "<group>"; };
		95EDFC05276C331400463484 /* XMLPatchFlowerChannel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPatchFlowerChannel.swift; sourceTree = "<group>"; };
		95EDFC07276C331E00463484 /* XMLPatchFlower.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPatchFlower.swift; sourceTree = "<group>"; };
		95EF6FF1276B637E00834441 /* XMLPatchScatter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPatchScatter.swift; sourceTree = "<group>"; };
		95F5D5FF24DD589D00A165CF /* XMLADXPreManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXPreManager.swift; sourceTree = "<group>"; };
		95F93AD8249A1AE40053B28C /* XMLADXQueueManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXQueueManager.swift; sourceTree = "<group>"; };
		95F93ADA249A2FD20053B28C /* XMLADXWidgetManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXWidgetManager.swift; sourceTree = "<group>"; };
		95FEFEA82609E74A0033F39A /* XMLRewardedVideoTBNormal.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLRewardedVideoTBNormal.swift; sourceTree = "<group>"; };
		95FEFEAA2609E80C0033F39A /* XMLRewardedVideoTBVip.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLRewardedVideoTBVip.swift; sourceTree = "<group>"; };
		9933EB91C1253A306630EEC2 /* Pods-XMADXModule.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XMADXModule.release.xcconfig"; path = "Target Support Files/Pods-XMADXModule/Pods-XMADXModule.release.xcconfig"; sourceTree = "<group>"; };
		A601B2B4814B141A6AF5E3E0 /* Pods-XMADXModule.alpha.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XMADXModule.alpha.xcconfig"; path = "Target Support Files/Pods-XMADXModule/Pods-XMADXModule.alpha.xcconfig"; sourceTree = "<group>"; };
		AE0AC646AD8E66464DB853D0 /* Pods_XMADXModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_XMADXModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B61ADBC92769C16C001743D1 /* XMLADXLoaderJDTransfer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXLoaderJDTransfer.swift; sourceTree = "<group>"; };
		B61B080527BE449200DE8017 /* XMLDRBroadcastADPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLDRBroadcastADPopupView.swift; sourceTree = "<group>"; };
		B62EE5E6278FD04E00331D49 /* XMLDRNativePlaqueAdTrigger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLDRNativePlaqueAdTrigger.swift; sourceTree = "<group>"; };
		B64ADF1B27955488005ACE22 /* XMLBUNativePlaqueAdTrigger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLBUNativePlaqueAdTrigger.swift; sourceTree = "<group>"; };
		B64ADF1D27957BA0005ACE22 /* XMLGDTNativePlaqueAdTrigger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLGDTNativePlaqueAdTrigger.swift; sourceTree = "<group>"; };
		B64B565A279025D9007847F1 /* XMLPlaqueAdView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPlaqueAdView.swift; sourceTree = "<group>"; };
		B64D20202769D522002E4E9C /* XMLJDSplashViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLJDSplashViewController.swift; sourceTree = "<group>"; };
		B64D2022276B1DE8002E4E9C /* XMLJDNativeAdTrigger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLJDNativeAdTrigger.swift; sourceTree = "<group>"; };
		B67E9CA5278D6D5000409D4E /* XMLSplashSlideMaskWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLSplashSlideMaskWidget.swift; sourceTree = "<group>"; };
		B683418B26E231AE0047BBDE /* XMLADXCustomLoaderMgr.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXCustomLoaderMgr.swift; sourceTree = "<group>"; };
		B68ACB22278FE93900FEBC6A /* XMLNativePlaqueAdTrigger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLNativePlaqueAdTrigger.swift; sourceTree = "<group>"; };
		B69715D32DF68D0C00A0CB27 /* XMLADXBiddingManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXBiddingManager.swift; sourceTree = "<group>"; };
		B69715D42DF68D0C00A0CB27 /* XMLADXBiddingStrategy.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLADXBiddingStrategy.swift; sourceTree = "<group>"; };
		B69DEAEF25021E7C00DB8380 /* MediaModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = MediaModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		BAD50E5EB3571EBCDAA7C5C4 /* Pods-XMADXModuleTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XMADXModuleTests.debug.xcconfig"; path = "Target Support Files/Pods-XMADXModuleTests/Pods-XMADXModuleTests.debug.xcconfig"; sourceTree = "<group>"; };
		CD43450C2E1386C20050E83C /* XMIAdABTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMIAdABTest.swift; sourceTree = "<group>"; };
		CD4345112E16710A0050E83C /* RNXMBusiness.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RNXMBusiness.swift; sourceTree = "<group>"; };
		D10311DF1944CAB200B07C32 /* Pods_XMADXModuleTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_XMADXModuleTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		EF41FA74B315E88CD1303277 /* Pods-XMADXModule.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XMADXModule.debug.xcconfig"; path = "Target Support Files/Pods-XMADXModule/Pods-XMADXModule.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		952F1CDD247F65AA0093B01D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				953A2FF9247FAC9A002FFA66 /* XMNetworkModule.framework in Frameworks */,
				B69DEAF025021E7C00DB8380 /* MediaModule.framework in Frameworks */,
				953A2FFB247FACA1002FFA66 /* BaseModule.framework in Frameworks */,
				953A2FF5247FAC86002FFA66 /* RouterModule.framework in Frameworks */,
				953A2FF7247FAC91002FFA66 /* XMConfigModule.framework in Frameworks */,
				953A2FF3247FAC7E002FFA66 /* XMUtilModule.framework in Frameworks */,
				3AAC1FBB06B955386AB5C8EA /* Pods_XMADXModule.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9573333D2484EB8200F105C0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				957333452484EB8200F105C0 /* XMADXModule.framework in Frameworks */,
				7620DB7990B5C69F63CF6C73 /* Pods_XMADXModuleTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		95D7A4F92535518800E3A67A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		139E2E8F5986D0B0FEF37C69 /* Pods */ = {
			isa = PBXGroup;
			children = (
				EF41FA74B315E88CD1303277 /* Pods-XMADXModule.debug.xcconfig */,
				9933EB91C1253A306630EEC2 /* Pods-XMADXModule.release.xcconfig */,
				BAD50E5EB3571EBCDAA7C5C4 /* Pods-XMADXModuleTests.debug.xcconfig */,
				469BFFB0A492F684750DC983 /* Pods-XMADXModuleTests.release.xcconfig */,
				A601B2B4814B141A6AF5E3E0 /* Pods-XMADXModule.alpha.xcconfig */,
				3F4ED1FC0527017B8831A77D /* Pods-XMADXModuleTests.alpha.xcconfig */,
			);
			name = Pods;
			path = ../Pods;
			sourceTree = "<group>";
		};
		22F0A59E26E221C900329785 /* Reward */ = {
			isa = PBXGroup;
			children = (
				22FC4DCF26E62BFF000B2199 /* XMLDRRewardVideoAd.swift */,
				22FC4DC826E5DC1C000B2199 /* XMLDRVideoPlayerViewController.swift */,
				22FC4DCA26E5DC1C000B2199 /* XMLDRBottomCardView.swift */,
				22FC4DC726E5DC1B000B2199 /* XMLDRVideoFinishedFrameAlert.swift */,
			);
			path = Reward;
			sourceTree = "<group>";
		};
		26CEF277E232226868D41879 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B69DEAEF25021E7C00DB8380 /* MediaModule.framework */,
				953A2FFC247FAE06002FFA66 /* UIKit.framework */,
				953A2FFA247FACA1002FFA66 /* BaseModule.framework */,
				953A2FF8247FAC9A002FFA66 /* XMNetworkModule.framework */,
				953A2FF6247FAC91002FFA66 /* XMConfigModule.framework */,
				953A2FF4247FAC86002FFA66 /* RouterModule.framework */,
				953A2FF2247FAC7E002FFA66 /* XMUtilModule.framework */,
				953A2FF0247FAC69002FFA66 /* XMADXSupportModule.framework */,
				953A2FEE247FAC4D002FFA66 /* AVKit.framework */,
				953A2FEC247FAC3B002FFA66 /* AVFoundation.framework */,
				953A2FEA247FAC33002FFA66 /* XMAVKit.framework */,
				953A2FE8247FAC2C002FFA66 /* Foundation.framework */,
				952F1D14247F840B0093B01D /* AudioModule.framework */,
				952F1D12247F84020093B01D /* SearchModule.framework */,
				952F1D10247F83F90093B01D /* MyListenModule.framework */,
				952F1D0E247F83EE0093B01D /* MineModule.framework */,
				952F1D0C247F83E70093B01D /* HomeModule.framework */,
				952F1D0A247F832A0093B01D /* BaseModule.framework */,
				952F1D08247F82F00093B01D /* RouterModule.framework */,
				952F1D06247F82CC0093B01D /* XMConfigModule.framework */,
				AE0AC646AD8E66464DB853D0 /* Pods_XMADXModule.framework */,
				D10311DF1944CAB200B07C32 /* Pods_XMADXModuleTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		952C99E6269697C300865B68 /* DR */ = {
			isa = PBXGroup;
			children = (
				22F0A59E26E221C900329785 /* Reward */,
				952C99F02696E54A00865B68 /* Common */,
				952C99EF2696E54000865B68 /* Model */,
				952C99EB2696D7E100865B68 /* XMLDRSplashView.swift */,
			);
			path = DR;
			sourceTree = "<group>";
		};
		952C99EF2696E54000865B68 /* Model */ = {
			isa = PBXGroup;
			children = (
				953A2FBD247FAB9E002FFA66 /* XMLDRNativeAd.swift */,
				952C9A1E2697150500865B68 /* XMLDRNativeEnum.swift */,
				952C9A24269B2BBD00865B68 /* XMLDRVideoManager.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		952C99F02696E54A00865B68 /* Common */ = {
			isa = PBXGroup;
			children = (
				953A2FBB247FAB9E002FFA66 /* XMLDRMediaView.swift */,
				95ACAC1A25417EF0002CA15F /* XMLDRSoundPlayer.swift */,
				952C99E7269699ED00865B68 /* XMLDRVideoPlayer.swift */,
				952C99F12696E5B900865B68 /* XMLDRReletedAdView.swift */,
				225A31FC26FB0588008009DF /* XMLDRShakeView.swift */,
				B61B080527BE449200DE8017 /* XMLDRBroadcastADPopupView.swift */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		952F1CD6247F65AA0093B01D = {
			isa = PBXGroup;
			children = (
				952F1CE2247F65AA0093B01D /* XMADXModule */,
				957333412484EB8200F105C0 /* XMADXModuleTests */,
				95D7A4FD2535518800E3A67A /* XMADXMedia */,
				952F1CE1247F65AA0093B01D /* Products */,
				139E2E8F5986D0B0FEF37C69 /* Pods */,
				26CEF277E232226868D41879 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		952F1CE1247F65AA0093B01D /* Products */ = {
			isa = PBXGroup;
			children = (
				952F1CE0247F65AA0093B01D /* XMADXModule.framework */,
				957333402484EB8200F105C0 /* XMADXModuleTests.xctest */,
				95D7A4FC2535518800E3A67A /* XMADXMedia.bundle */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		952F1CE2247F65AA0093B01D /* XMADXModule */ = {
			isa = PBXGroup;
			children = (
				CD4345102E1670CF0050E83C /* RN */,
				CD4345092E13867D0050E83C /* ABTest */,
				953A2FCF247FAB9F002FFA66 /* AD */,
				953A2FC4247FAB9E002FFA66 /* ADX */,
				953A2FB9247FAB9E002FFA66 /* ADXWidget */,
				95ACAC2025426F20002CA15F /* ADXBusiness */,
				952C99E6269697C300865B68 /* DR */,
				952F1CE3247F65AA0093B01D /* XMADXModule.h */,
				952F1CE4247F65AA0093B01D /* Info.plist */,
				95CBBC1A24F7CD8A00D5D05B /* XMADXAssets.xcassets */,
			);
			path = XMADXModule;
			sourceTree = "<group>";
		};
		953A2FB9247FAB9E002FFA66 /* ADXWidget */ = {
			isa = PBXGroup;
			children = (
				95EF6FF0276B631B00834441 /* Scatter */,
				95F93ADA249A2FD20053B28C /* XMLADXWidgetManager.swift */,
				95DCED242726D881006AE1CB /* Plaque */,
				95CBBC0324F6403500D5D05B /* PaidUnlock */,
				95756F6124E119080089B6A0 /* Splash */,
				95756F6224E119150089B6A0 /* RewardV */,
				95756F6324E1193A0089B6A0 /* Flow */,
			);
			path = ADXWidget;
			sourceTree = "<group>";
		};
		953A2FC4247FAB9E002FFA66 /* ADX */ = {
			isa = PBXGroup;
			children = (
				B69715D52DF68D0C00A0CB27 /* Bidding */,
				953A2FC6247FAB9E002FFA66 /* XMLADXManager.swift */,
				95756F6424E119E50089B6A0 /* Pretreat */,
				95F5D5FE24DD572500A165CF /* Loader */,
				9579A4B424DD2FD70021751B /* LoaderManager */,
				9579A49E24DD1EF70021751B /* Transfer */,
				9579A49F24DD1F240021751B /* XMLADXCacheManager.swift */,
				953A2FCA247FAB9E002FFA66 /* XMLADXReportManager.swift */,
				9579A4BA24DD3D130021751B /* XMLADXPreDataManager.swift */,
				95F93AD8249A1AE40053B28C /* XMLADXQueueManager.swift */,
				953A2FCC247FAB9E002FFA66 /* XMLADXMacro.swift */,
				9540350024A1D0E40005316A /* XMLADXEnum.swift */,
				953A2FCE247FAB9E002FFA66 /* XMLADXUtils.swift */,
				95E1AF632488ED5E00E78CCE /* XMLADXConfig.swift */,
				9506FCC926809AE6007A46FE /* XMLADXNotification.swift */,
				950A5B732600D85000972866 /* XMLADXProtocol.swift */,
				95ACAC25254273B3002CA15F /* XMLADXSafetyGroup.swift */,
				957D1BE52580E84E0036ECC0 /* XMLADXFreeManager.swift */,
				9556BD9A259B20990099963B /* XMLADXErrorCodeManager.swift */,
				95A77FAE26DDFEC40049299B /* XMLADXRTBManager.swift */,
			);
			path = ADX;
			sourceTree = "<group>";
		};
		953A2FCF247FAB9F002FFA66 /* AD */ = {
			isa = PBXGroup;
			children = (
				953A2FD1247FAB9F002FFA66 /* XMLAdRequestManager.swift */,
				95756F6724E11C270089B6A0 /* XMLAdRequest.swift */,
				95756F6524E11A780089B6A0 /* XMLAdRequestData.swift */,
				95D5A7C224985E8200257D6A /* XMLAdRequestUtils.swift */,
				95E1AF65248A18E800E78CCE /* XMLAdRequestSubModels.swift */,
				95CBBC2524F8F0D900D5D05B /* XMLPulAdRequestManager.swift */,
				95CBBC2124F8A37F00D5D05B /* XMLPulAdRequest.swift */,
			);
			path = AD;
			sourceTree = "<group>";
		};
		957333412484EB8200F105C0 /* XMADXModuleTests */ = {
			isa = PBXGroup;
			children = (
				957333422484EB8200F105C0 /* XMADXModuleTests.swift */,
				957333442484EB8200F105C0 /* Info.plist */,
			);
			path = XMADXModuleTests;
			sourceTree = "<group>";
		};
		95756F6124E119080089B6A0 /* Splash */ = {
			isa = PBXGroup;
			children = (
				953A2FBE247FAB9E002FFA66 /* XMLBUSplashViewController.swift */,
				953A2FC1247FAB9E002FFA66 /* XMLGDTSplashViewTrigger.swift */,
				953A2FBA247FAB9E002FFA66 /* XMLDRSplashViewController.swift */,
				95D4018B248F90060093F596 /* XMLBDSplashViewController.swift */,
				B64D20202769D522002E4E9C /* XMLJDSplashViewController.swift */,
				95E5337225F730D900892209 /* XMLSplashJumpMaskWidget.swift */,
				B67E9CA5278D6D5000409D4E /* XMLSplashSlideMaskWidget.swift */,
			);
			path = Splash;
			sourceTree = "<group>";
		};
		95756F6224E119150089B6A0 /* RewardV */ = {
			isa = PBXGroup;
			children = (
				953A2FC2247FAB9E002FFA66 /* XMLBURewardedVideoAdTrigger.swift */,
				953A2FC3247FAB9E002FFA66 /* XMLGDTRewardedVideoAdTrigger.swift */,
				950794362491DBBE00E054D0 /* XMLBDRewardedVideoAdTrigger.swift */,
				950819FA26E658B600C60D40 /* XMLDRRewardedVideoAdTrigger.swift */,
				958D243E24FCE80900BB2DB9 /* XMLRewardedVideoAdTrigger.swift */,
				95E532C925F6056300892209 /* XMLVideoADFullTopMask.swift */,
				95654AAB2678B05600717A02 /* XMLVideoADFreeTopMask.swift */,
				95A88D5F26A170A3001881D0 /* XMLVideoADClaimTopMask.swift */,
			);
			path = RewardV;
			sourceTree = "<group>";
		};
		95756F6324E1193A0089B6A0 /* Flow */ = {
			isa = PBXGroup;
			children = (
				956CB78F264CCFE7004AC7BA /* XMLNativeAdTrigger.swift */,
				953A2FBC247FAB9E002FFA66 /* XMLBUNativeAdTrigger.swift */,
				953A2FBF247FAB9E002FFA66 /* XMLGDTNativeAdTrigger.swift */,
				953A2FC0247FAB9E002FFA66 /* XMLDRNativeAdTrigger.swift */,
				95079434249084C100E054D0 /* XMLBDNativeAdTrigger.swift */,
				B64D2022276B1DE8002E4E9C /* XMLJDNativeAdTrigger.swift */,
				95A2559124938ED3008B47E7 /* XMLBDNativeAdLogoView.swift */,
				957D1BE32580A1CC0036ECC0 /* XMLADFreeMask.swift */,
			);
			path = Flow;
			sourceTree = "<group>";
		};
		95756F6424E119E50089B6A0 /* Pretreat */ = {
			isa = PBXGroup;
			children = (
				95F5D5FF24DD589D00A165CF /* XMLADXPreManager.swift */,
				95756F5F24E115FE0089B6A0 /* XMLADXPretreater.swift */,
			);
			path = Pretreat;
			sourceTree = "<group>";
		};
		9579A49E24DD1EF70021751B /* Transfer */ = {
			isa = PBXGroup;
			children = (
				953A2FC7247FAB9E002FFA66 /* XMLADXLoaderBUTransfer.swift */,
				953A2FCB247FAB9E002FFA66 /* XMLADXLoaderGDTTransfer.swift */,
				953A2FC5247FAB9E002FFA66 /* XMLADXLoaderDRTransfer.swift */,
				95D40189248F77FB0093F596 /* XMLADXLoaderBDTransfer.swift */,
				B61ADBC92769C16C001743D1 /* XMLADXLoaderJDTransfer.swift */,
			);
			path = Transfer;
			sourceTree = "<group>";
		};
		9579A4B424DD2FD70021751B /* LoaderManager */ = {
			isa = PBXGroup;
			children = (
				B683418B26E231AE0047BBDE /* XMLADXCustomLoaderMgr.swift */,
				953A2FC8247FAB9E002FFA66 /* XMLADXLoaderManager.swift */,
				9579A4B824DD36130021751B /* XMLADXPreLoaderManager.swift */,
				9579A4B624DD301B0021751B /* XMLADXBaseLoaderManager.swift */,
				95CBBC2324F8D48900D5D05B /* XMLADXPulLoaderManager.swift */,
			);
			path = LoaderManager;
			sourceTree = "<group>";
		};
		959148E1255A3F650069AD38 /* Media */ = {
			isa = PBXGroup;
			children = (
				959148E2255A3F780069AD38 /* XMLADXMediaManager.swift */,
				959148E4255A3F8C0069AD38 /* XMLADXMediaStaff.swift */,
			);
			path = Media;
			sourceTree = "<group>";
		};
		95ACAC2025426F20002CA15F /* ADXBusiness */ = {
			isa = PBXGroup;
			children = (
				959148E1255A3F650069AD38 /* Media */,
				95ACAC2125427217002CA15F /* Sound */,
			);
			path = ADXBusiness;
			sourceTree = "<group>";
		};
		95ACAC2125427217002CA15F /* Sound */ = {
			isa = PBXGroup;
			children = (
				9504F66C252455C900C76721 /* XMLADXSoundManager.swift */,
				95ACAC222542731A002CA15F /* XMLADXSoundStaff.swift */,
			);
			path = Sound;
			sourceTree = "<group>";
		};
		95CBBC0324F6403500D5D05B /* PaidUnlock */ = {
			isa = PBXGroup;
			children = (
				95CBBC0424F6408400D5D05B /* XMLRewardedVideoToolBar.swift */,
				95FEFEA82609E74A0033F39A /* XMLRewardedVideoTBNormal.swift */,
				95FEFEAA2609E80C0033F39A /* XMLRewardedVideoTBVip.swift */,
				95CBBC1F24F7D0DE00D5D05B /* XMLRewardedVideoAlert.swift */,
			);
			path = PaidUnlock;
			sourceTree = "<group>";
		};
		95D7A4FD2535518800E3A67A /* XMADXMedia */ = {
			isa = PBXGroup;
			children = (
				95D7A4FE2535518800E3A67A /* Info.plist */,
			);
			path = XMADXMedia;
			sourceTree = "<group>";
		};
		95DCED242726D881006AE1CB /* Plaque */ = {
			isa = PBXGroup;
			children = (
				95DCED252726D8B3006AE1CB /* XMLBUPlaqueAdTrigger.swift */,
				95DCED272726D8BF006AE1CB /* XMLGDTPlaqueAdTrigger.swift */,
				B68ACB22278FE93900FEBC6A /* XMLNativePlaqueAdTrigger.swift */,
				B62EE5E6278FD04E00331D49 /* XMLDRNativePlaqueAdTrigger.swift */,
				B64ADF1B27955488005ACE22 /* XMLBUNativePlaqueAdTrigger.swift */,
				B64ADF1D27957BA0005ACE22 /* XMLGDTNativePlaqueAdTrigger.swift */,
				B64B565A279025D9007847F1 /* XMLPlaqueAdView.swift */,
			);
			path = Plaque;
			sourceTree = "<group>";
		};
		95EF6FF0276B631B00834441 /* Scatter */ = {
			isa = PBXGroup;
			children = (
				95EF6FF1276B637E00834441 /* XMLPatchScatter.swift */,
				95EDFC05276C331400463484 /* XMLPatchFlowerChannel.swift */,
				95EDFC07276C331E00463484 /* XMLPatchFlower.swift */,
			);
			path = Scatter;
			sourceTree = "<group>";
		};
		95F5D5FE24DD572500A165CF /* Loader */ = {
			isa = PBXGroup;
			children = (
				953A2FCD247FAB9E002FFA66 /* XMLADXLoader.swift */,
				953A2FC9247FAB9E002FFA66 /* XMLADXLoaderItem.swift */,
			);
			path = Loader;
			sourceTree = "<group>";
		};
		B69715D52DF68D0C00A0CB27 /* Bidding */ = {
			isa = PBXGroup;
			children = (
				B69715D32DF68D0C00A0CB27 /* XMLADXBiddingManager.swift */,
				B69715D42DF68D0C00A0CB27 /* XMLADXBiddingStrategy.swift */,
			);
			path = Bidding;
			sourceTree = "<group>";
		};
		CD4345092E13867D0050E83C /* ABTest */ = {
			isa = PBXGroup;
			children = (
				CD43450C2E1386C20050E83C /* XMIAdABTest.swift */,
			);
			path = ABTest;
			sourceTree = "<group>";
		};
		CD4345102E1670CF0050E83C /* RN */ = {
			isa = PBXGroup;
			children = (
				CD4345112E16710A0050E83C /* RNXMBusiness.swift */,
			);
			path = RN;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		952F1CDB247F65AA0093B01D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				952F1CE5247F65AA0093B01D /* XMADXModule.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		952F1CDF247F65AA0093B01D /* XMADXModule */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 952F1CE8247F65AA0093B01D /* Build configuration list for PBXNativeTarget "XMADXModule" */;
			buildPhases = (
				5670E5C859C8362E89F921AC /* [CP] Check Pods Manifest.lock */,
				952F1CDB247F65AA0093B01D /* Headers */,
				952F1CDC247F65AA0093B01D /* Sources */,
				952F1CDD247F65AA0093B01D /* Frameworks */,
				952F1CDE247F65AA0093B01D /* Resources */,
				AB9A8CFAC1D6FBAC1C078543 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				95452B5625357451007DFA68 /* PBXTargetDependency */,
			);
			name = XMADXModule;
			productName = XMADXModule;
			productReference = 952F1CE0247F65AA0093B01D /* XMADXModule.framework */;
			productType = "com.apple.product-type.framework";
		};
		9573333F2484EB8200F105C0 /* XMADXModuleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9573334A2484EB8300F105C0 /* Build configuration list for PBXNativeTarget "XMADXModuleTests" */;
			buildPhases = (
				64021DE341123B62729463C9 /* [CP] Check Pods Manifest.lock */,
				9573333C2484EB8200F105C0 /* Sources */,
				9573333D2484EB8200F105C0 /* Frameworks */,
				9573333E2484EB8200F105C0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				957333472484EB8200F105C0 /* PBXTargetDependency */,
			);
			name = XMADXModuleTests;
			productName = XMADXModuleTests;
			productReference = 957333402484EB8200F105C0 /* XMADXModuleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		95D7A4FB2535518800E3A67A /* XMADXMedia */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 95D7A5022535518800E3A67A /* Build configuration list for PBXNativeTarget "XMADXMedia" */;
			buildPhases = (
				95D7A4F82535518800E3A67A /* Sources */,
				95D7A4F92535518800E3A67A /* Frameworks */,
				95D7A4FA2535518800E3A67A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = XMADXMedia;
			productName = XMADXMedia;
			productReference = 95D7A4FC2535518800E3A67A /* XMADXMedia.bundle */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		952F1CD7247F65AA0093B01D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				DefaultBuildSystemTypeForWorkspace = Original;
				LastSwiftUpdateCheck = 1130;
				LastUpgradeCheck = 1130;
				ORGANIZATIONNAME = ximalaya;
				TargetAttributes = {
					952F1CDF247F65AA0093B01D = {
						CreatedOnToolsVersion = 11.3;
					};
					9573333F2484EB8200F105C0 = {
						CreatedOnToolsVersion = 11.3;
					};
					95D7A4FB2535518800E3A67A = {
						CreatedOnToolsVersion = 11.4.1;
					};
				};
			};
			buildConfigurationList = 952F1CDA247F65AA0093B01D /* Build configuration list for PBXProject "XMADXModule" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 952F1CD6247F65AA0093B01D;
			productRefGroup = 952F1CE1247F65AA0093B01D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				952F1CDF247F65AA0093B01D /* XMADXModule */,
				9573333F2484EB8200F105C0 /* XMADXModuleTests */,
				95D7A4FB2535518800E3A67A /* XMADXMedia */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		952F1CDE247F65AA0093B01D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95452B5725357499007DFA68 /* XMADXMedia.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9573333E2484EB8200F105C0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		95D7A4FA2535518800E3A67A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95D7A5032535557800E3A67A /* XMADXAssets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		5670E5C859C8362E89F921AC /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-XMADXModule-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		64021DE341123B62729463C9 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-XMADXModuleTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		AB9A8CFAC1D6FBAC1C078543 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-XMADXModule/Pods-XMADXModule-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-XMADXModule/Pods-XMADXModule-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-XMADXModule/Pods-XMADXModule-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		952F1CDC247F65AA0093B01D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				953A2FDA247FAB9F002FFA66 /* XMLBURewardedVideoAdTrigger.swift in Sources */,
				95F93ADB249A2FD20053B28C /* XMLADXWidgetManager.swift in Sources */,
				B68ACB23278FE93900FEBC6A /* XMLNativePlaqueAdTrigger.swift in Sources */,
				95ACAC26254273B3002CA15F /* XMLADXSafetyGroup.swift in Sources */,
				95CBBC2624F8F0DA00D5D05B /* XMLPulAdRequestManager.swift in Sources */,
				22FC4DCB26E5DC1C000B2199 /* XMLDRVideoFinishedFrameAlert.swift in Sources */,
				225A31FD26FB0588008009DF /* XMLDRShakeView.swift in Sources */,
				953A2FD7247FAB9F002FFA66 /* XMLGDTNativeAdTrigger.swift in Sources */,
				95ACAC232542731A002CA15F /* XMLADXSoundStaff.swift in Sources */,
				953A2FD6247FAB9F002FFA66 /* XMLBUSplashViewController.swift in Sources */,
				9504F66D252455CA00C76721 /* XMLADXSoundManager.swift in Sources */,
				95E532CA25F6056300892209 /* XMLVideoADFullTopMask.swift in Sources */,
				95A88D6026A170A3001881D0 /* XMLVideoADClaimTopMask.swift in Sources */,
				950794372491DBBE00E054D0 /* XMLBDRewardedVideoAdTrigger.swift in Sources */,
				958D243F24FCE80A00BB2DB9 /* XMLRewardedVideoAdTrigger.swift in Sources */,
				CD43450D2E1386C40050E83C /* XMIAdABTest.swift in Sources */,
				95079435249084C100E054D0 /* XMLBDNativeAdTrigger.swift in Sources */,
				953A2FE5247FAB9F002FFA66 /* XMLADXUtils.swift in Sources */,
				95A77FAF26DDFEC40049299B /* XMLADXRTBManager.swift in Sources */,
				950819FB26E658B600C60D40 /* XMLDRRewardedVideoAdTrigger.swift in Sources */,
				95D5A7C324985E8200257D6A /* XMLAdRequestUtils.swift in Sources */,
				9579A4BB24DD3D130021751B /* XMLADXPreDataManager.swift in Sources */,
				B69715D62DF68D0C00A0CB27 /* XMLADXBiddingStrategy.swift in Sources */,
				B69715D82DF68D0C00A0CB27 /* XMLADXBiddingManager.swift in Sources */,
				22FC4DD026E62C00000B2199 /* XMLDRRewardVideoAd.swift in Sources */,
				95EDFC06276C331400463484 /* XMLPatchFlowerChannel.swift in Sources */,
				953A2FDD247FAB9F002FFA66 /* XMLADXManager.swift in Sources */,
				953A2FD9247FAB9F002FFA66 /* XMLGDTSplashViewTrigger.swift in Sources */,
				22FC4DCC26E5DC1C000B2199 /* XMLDRVideoPlayerViewController.swift in Sources */,
				B64ADF1C27955488005ACE22 /* XMLBUNativePlaqueAdTrigger.swift in Sources */,
				CD4345122E16711D0050E83C /* RNXMBusiness.swift in Sources */,
				B683418C26E231AE0047BBDE /* XMLADXCustomLoaderMgr.swift in Sources */,
				959148E3255A3F780069AD38 /* XMLADXMediaManager.swift in Sources */,
				953A2FE0247FAB9F002FFA66 /* XMLADXLoaderItem.swift in Sources */,
				953A2FE3247FAB9F002FFA66 /* XMLADXMacro.swift in Sources */,
				95D4018A248F77FB0093F596 /* XMLADXLoaderBDTransfer.swift in Sources */,
				953A2FE7247FAB9F002FFA66 /* XMLAdRequestManager.swift in Sources */,
				9579A4B924DD36140021751B /* XMLADXPreLoaderManager.swift in Sources */,
				9579A4B724DD301B0021751B /* XMLADXBaseLoaderManager.swift in Sources */,
				95F5D60024DD589D00A165CF /* XMLADXPreManager.swift in Sources */,
				953A2FD2247FAB9F002FFA66 /* XMLDRSplashViewController.swift in Sources */,
				95DCED282726D8BF006AE1CB /* XMLGDTPlaqueAdTrigger.swift in Sources */,
				95E5337325F730D900892209 /* XMLSplashJumpMaskWidget.swift in Sources */,
				B64D20212769D522002E4E9C /* XMLJDSplashViewController.swift in Sources */,
				953A2FDF247FAB9F002FFA66 /* XMLADXLoaderManager.swift in Sources */,
				952C99E8269699ED00865B68 /* XMLDRVideoPlayer.swift in Sources */,
				95E1AF642488ED5E00E78CCE /* XMLADXConfig.swift in Sources */,
				95EF6FF2276B637E00834441 /* XMLPatchScatter.swift in Sources */,
				95A2559224938ED3008B47E7 /* XMLBDNativeAdLogoView.swift in Sources */,
				95CBBC2224F8A38000D5D05B /* XMLPulAdRequest.swift in Sources */,
				952C99EC2696D7E100865B68 /* XMLDRSplashView.swift in Sources */,
				B64B565B279025D9007847F1 /* XMLPlaqueAdView.swift in Sources */,
				95CBBC2424F8D48A00D5D05B /* XMLADXPulLoaderManager.swift in Sources */,
				953A2FE2247FAB9F002FFA66 /* XMLADXLoaderGDTTransfer.swift in Sources */,
				953A2FDB247FAB9F002FFA66 /* XMLGDTRewardedVideoAdTrigger.swift in Sources */,
				95DCED262726D8B3006AE1CB /* XMLBUPlaqueAdTrigger.swift in Sources */,
				952C9A1F2697150500865B68 /* XMLDRNativeEnum.swift in Sources */,
				956CB790264CCFE7004AC7BA /* XMLNativeAdTrigger.swift in Sources */,
				953A2FE1247FAB9F002FFA66 /* XMLADXReportManager.swift in Sources */,
				95654AAC2678B05600717A02 /* XMLVideoADFreeTopMask.swift in Sources */,
				953A2FD8247FAB9F002FFA66 /* XMLDRNativeAdTrigger.swift in Sources */,
				B67E9CA6278D6D5000409D4E /* XMLSplashSlideMaskWidget.swift in Sources */,
				959148E5255A3F8C0069AD38 /* XMLADXMediaStaff.swift in Sources */,
				95756F6624E11A780089B6A0 /* XMLAdRequestData.swift in Sources */,
				B61B080627BE449200DE8017 /* XMLDRBroadcastADPopupView.swift in Sources */,
				B64ADF1E27957BA0005ACE22 /* XMLGDTNativePlaqueAdTrigger.swift in Sources */,
				9506FCCA26809AE6007A46FE /* XMLADXNotification.swift in Sources */,
				95CBBC0524F6408400D5D05B /* XMLRewardedVideoToolBar.swift in Sources */,
				953A2FD5247FAB9F002FFA66 /* XMLDRNativeAd.swift in Sources */,
				95CBBC2024F7D0DE00D5D05B /* XMLRewardedVideoAlert.swift in Sources */,
				953A2FE4247FAB9F002FFA66 /* XMLADXLoader.swift in Sources */,
				9579A4A024DD1F240021751B /* XMLADXCacheManager.swift in Sources */,
				95ACAC1B25417EF0002CA15F /* XMLDRSoundPlayer.swift in Sources */,
				95E1AF66248A18E800E78CCE /* XMLAdRequestSubModels.swift in Sources */,
				B62EE5E7278FD04E00331D49 /* XMLDRNativePlaqueAdTrigger.swift in Sources */,
				957D1BE42580A1CC0036ECC0 /* XMLADFreeMask.swift in Sources */,
				95FEFEA92609E74A0033F39A /* XMLRewardedVideoTBNormal.swift in Sources */,
				B61ADBCA2769C16C001743D1 /* XMLADXLoaderJDTransfer.swift in Sources */,
				95756F6824E11C270089B6A0 /* XMLAdRequest.swift in Sources */,
				95D4018C248F90060093F596 /* XMLBDSplashViewController.swift in Sources */,
				95FEFEAB2609E80C0033F39A /* XMLRewardedVideoTBVip.swift in Sources */,
				9540350124A1D0E40005316A /* XMLADXEnum.swift in Sources */,
				953A2FD3247FAB9F002FFA66 /* XMLDRMediaView.swift in Sources */,
				9556BD9B259B209A0099963B /* XMLADXErrorCodeManager.swift in Sources */,
				22FC4DCE26E5DC1C000B2199 /* XMLDRBottomCardView.swift in Sources */,
				957D1BE62580E84E0036ECC0 /* XMLADXFreeManager.swift in Sources */,
				953A2FD4247FAB9F002FFA66 /* XMLBUNativeAdTrigger.swift in Sources */,
				95756F6024E115FF0089B6A0 /* XMLADXPretreater.swift in Sources */,
				952C99F22696E5B900865B68 /* XMLDRReletedAdView.swift in Sources */,
				950A5B742600D85000972866 /* XMLADXProtocol.swift in Sources */,
				95F93AD9249A1AE40053B28C /* XMLADXQueueManager.swift in Sources */,
				953A2FDE247FAB9F002FFA66 /* XMLADXLoaderBUTransfer.swift in Sources */,
				953A2FDC247FAB9F002FFA66 /* XMLADXLoaderDRTransfer.swift in Sources */,
				B64D2023276B1DE8002E4E9C /* XMLJDNativeAdTrigger.swift in Sources */,
				952C9A25269B2BBD00865B68 /* XMLDRVideoManager.swift in Sources */,
				95EDFC08276C331E00463484 /* XMLPatchFlower.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9573333C2484EB8200F105C0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				957333432484EB8200F105C0 /* XMADXModuleTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		95D7A4F82535518800E3A67A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		95452B5625357451007DFA68 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 95D7A4FB2535518800E3A67A /* XMADXMedia */;
			targetProxy = 95452B5525357451007DFA68 /* PBXContainerItemProxy */;
		};
		957333472484EB8200F105C0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 952F1CDF247F65AA0093B01D /* XMADXModule */;
			targetProxy = 957333462484EB8200F105C0 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		952F1CE6247F65AA0093B01D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		952F1CE7247F65AA0093B01D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		952F1CE9247F65AA0093B01D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EF41FA74B315E88CD1303277 /* Pods-XMADXModule.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FDFullscreenPopGesture\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMKingfisherWebP\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMPKHUD\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMTingModel\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMUIKit\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/iCarousel\"",
					"\"${PODS_ROOT}/BaiduMobAdSDK/Frameworks\"",
					"\"${PODS_ROOT}/Bytedance-UnionAD/Frameworks\"",
					"\"${PODS_ROOT}/GDTMobSDK/Frameworks\"",
					"\"${PODS_ROOT}/KSAdSDK/Frameworks\"",
					"\"${PODS_ROOT}/PhoneNetSDK/frameworks\"",
					"\"${PODS_ROOT}/XM3rdParty/XM3rdParty/Products/iOS\"",
					"\"${PODS_ROOT}/XMBase/XMBase/Products\"",
					"\"${PODS_ROOT}/XMCategories\"",
					"\"${PODS_ROOT}/XMDataBase/XMDataBase/Products\"",
					"\"${PODS_ROOT}/XMThirdParty/XMThirdParty/Classes/TencentOpenAPI_v3.3.9\"",
					"\"${PODS_ROOT}/XMThirdParty/XMThirdParty/Classes/WebP\"",
					"\"${PODS_ROOT}/XMWebImage\"",
					"\"$(SRCROOT)/../Products\"",
				);
				INFOPLIST_FILE = XMADXModule/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMADXModule;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		952F1CEA247F65AA0093B01D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9933EB91C1253A306630EEC2 /* Pods-XMADXModule.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FDFullscreenPopGesture\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMKingfisherWebP\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMPKHUD\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMTingModel\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMUIKit\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/iCarousel\"",
					"\"${PODS_ROOT}/BaiduMobAdSDK/Frameworks\"",
					"\"${PODS_ROOT}/Bytedance-UnionAD/Frameworks\"",
					"\"${PODS_ROOT}/GDTMobSDK/Frameworks\"",
					"\"${PODS_ROOT}/KSAdSDK/Frameworks\"",
					"\"${PODS_ROOT}/PhoneNetSDK/frameworks\"",
					"\"${PODS_ROOT}/XM3rdParty/XM3rdParty/Products/iOS\"",
					"\"${PODS_ROOT}/XMBase/XMBase/Products\"",
					"\"${PODS_ROOT}/XMCategories\"",
					"\"${PODS_ROOT}/XMDataBase/XMDataBase/Products\"",
					"\"${PODS_ROOT}/XMThirdParty/XMThirdParty/Classes/TencentOpenAPI_v3.3.9\"",
					"\"${PODS_ROOT}/XMThirdParty/XMThirdParty/Classes/WebP\"",
					"\"${PODS_ROOT}/XMWebImage\"",
					"\"$(SRCROOT)/../Products\"",
				);
				INFOPLIST_FILE = XMADXModule/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMADXModule;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		957333482484EB8300F105C0 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BAD50E5EB3571EBCDAA7C5C4 /* Pods-XMADXModuleTests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = XMADXModuleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMADXModuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		957333492484EB8300F105C0 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 469BFFB0A492F684750DC983 /* Pods-XMADXModuleTests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = XMADXModuleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMADXModuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		95D7A4FF2535518800E3A67A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = XMADXMedia/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMADXMedia;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		95D7A5002535518800E3A67A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = XMADXMedia/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMADXMedia;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		95D7A5012535518800E3A67A /* Alpha */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = XMADXMedia/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMADXMedia;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "";
				WRAPPER_EXTENSION = bundle;
			};
			name = Alpha;
		};
		95F721D625020A7E0033112B /* Alpha */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Alpha;
		};
		95F721D725020A7E0033112B /* Alpha */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A601B2B4814B141A6AF5E3E0 /* Pods-XMADXModule.alpha.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FDFullscreenPopGesture\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMKingfisherWebP\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMPKHUD\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMTingModel\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMUIKit\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/iCarousel\"",
					"\"${PODS_ROOT}/BaiduMobAdSDK/Frameworks\"",
					"\"${PODS_ROOT}/Bytedance-UnionAD/Frameworks\"",
					"\"${PODS_ROOT}/GDTMobSDK/Frameworks\"",
					"\"${PODS_ROOT}/KSAdSDK/Frameworks\"",
					"\"${PODS_ROOT}/PhoneNetSDK/frameworks\"",
					"\"${PODS_ROOT}/XM3rdParty/XM3rdParty/Products/iOS\"",
					"\"${PODS_ROOT}/XMBase/XMBase/Products\"",
					"\"${PODS_ROOT}/XMCategories\"",
					"\"${PODS_ROOT}/XMDataBase/XMDataBase/Products\"",
					"\"${PODS_ROOT}/XMThirdParty/XMThirdParty/Classes/TencentOpenAPI_v3.3.9\"",
					"\"${PODS_ROOT}/XMThirdParty/XMThirdParty/Classes/WebP\"",
					"\"${PODS_ROOT}/XMWebImage\"",
					"\"$(SRCROOT)/../Products\"",
				);
				INFOPLIST_FILE = XMADXModule/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMADXModule;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Alpha;
		};
		95F721D825020A7E0033112B /* Alpha */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3F4ED1FC0527017B8831A77D /* Pods-XMADXModuleTests.alpha.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = XMADXModuleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMADXModuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Alpha;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		952F1CDA247F65AA0093B01D /* Build configuration list for PBXProject "XMADXModule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				952F1CE6247F65AA0093B01D /* Debug */,
				952F1CE7247F65AA0093B01D /* Release */,
				95F721D625020A7E0033112B /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		952F1CE8247F65AA0093B01D /* Build configuration list for PBXNativeTarget "XMADXModule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				952F1CE9247F65AA0093B01D /* Debug */,
				952F1CEA247F65AA0093B01D /* Release */,
				95F721D725020A7E0033112B /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9573334A2484EB8300F105C0 /* Build configuration list for PBXNativeTarget "XMADXModuleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				957333482484EB8300F105C0 /* Debug */,
				957333492484EB8300F105C0 /* Release */,
				95F721D825020A7E0033112B /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		95D7A5022535518800E3A67A /* Build configuration list for PBXNativeTarget "XMADXMedia" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				95D7A4FF2535518800E3A67A /* Debug */,
				95D7A5002535518800E3A67A /* Release */,
				95D7A5012535518800E3A67A /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 952F1CD7247F65AA0093B01D /* Project object */;
}
