//
//  RNXMBusiness.swift
//  XMADXModule
//
//  Created by xiaodong2.zhang on 2025/7/3.
//  Copyright © 2025 ximalaya. All rights reserved.
//

import Foundation

@objc(RNXMBusinessSwift)
public class RNXMBusinessSwift: NSObject {

    @objc public func listenEarnRewardCoinNew(_ params: [String: Any],
                                             resolve: @escaping (Any?) -> Void,
                                             reject: @escaping (String, String, Error?) -> Void) {
        // 解析参数
        let rewardVideoStyle = params["rewardVideoStyle"] as? Int ?? 0
        let rewardVideoStyleNew = rewardVideoStyle == 1
        let positionName = params["positionName"] as? String ?? "integral_center_inspire_video"

        // 模拟成功响应，测试桥接
        let responseData: [String: Any] = [
            "clientCode": "",
        ]

        print("✅ 返回测试数据: \(responseData)")
        //resolve(responseData)
        reject("-1","fail",nil)
    }
}
