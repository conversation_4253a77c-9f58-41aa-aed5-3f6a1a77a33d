//
//  RNXMBusiness.swift
//  XMADXModule
//
//  Created by xiaodong2.zhang on 2025/7/3.
//  Copyright © 2025 ximalaya. All rights reserved.
//

import Foundation

/// Swift 实现的 RN Business 模块，桥接 OC 接口
@objc(RNXMBusinessBridgeModule)
public class RNXMBusinessBridgeModule: NSObject {

    // MARK: - RCTBridgeModule 协议实现

    @objc public static func moduleName() -> String {
        return "Business"
    }

    @objc public static func requiresMainQueueSetup() -> Bool {
        return false
    }

    // MARK: - 导出方法

    /// 监听赚取奖励金币（新版）- 测试桥接功能
    /// - Parameters:
    ///   - params: 参数字典，包含 rewardVideoStyle 和 positionName
    ///   - resolve: 成功回调
    ///   - reject: 失败回调
    @objc public func listenEarnRewardCoinNew(_ params: [String: Any],
                                             resolve: @escaping (Any?) -> Void,
                                             reject: @escaping (String, String, Error?) -> Void) {

        print("🎯 Swift listenEarnRewardCoinNew 被调用了！")
        print("📦 接收到的参数: \(params)")

        // 解析参数
        let rewardVideoStyle = params["rewardVideoStyle"] as? Int ?? 0
        let rewardVideoStyleNew = rewardVideoStyle == 1
        let positionName = params["positionName"] as? String ?? "integral_center_inspire_video"

        print("🔧 解析后的参数:")
        print("   - rewardVideoStyle: \(rewardVideoStyle)")
        print("   - rewardVideoStyleNew: \(rewardVideoStyleNew)")
        print("   - positionName: \(positionName)")

        // 模拟成功响应，测试桥接
        let responseData: [String: Any] = [
            "success": true,
            "message": "Swift 桥接测试成功！",
            "rewardVideoStyleNew": rewardVideoStyleNew,
            "positionName": positionName,
            "timestamp": Date().timeIntervalSince1970
        ]

        print("✅ 返回测试数据: \(responseData)")
        resolve(responseData)
    }

    // MARK: - 测试方法

    /// 返回常量 - 用于测试
    @objc public func constantsToExport() -> [String: Any] {
        print("🔧 constantsToExport 被调用")
        return [
            "DEFAULT_POSITION_NAME": "integral_center_inspire_video",
            "REWARD_VIDEO_STYLE_OLD": 0,
            "REWARD_VIDEO_STYLE_NEW": 1,
            "BRIDGE_TEST": "Swift 桥接正常工作"
        ]
    }
}
