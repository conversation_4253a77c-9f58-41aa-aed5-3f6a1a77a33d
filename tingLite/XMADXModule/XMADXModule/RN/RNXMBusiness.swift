//
//  RNXMBusiness.swift
//  XMADXModule
//
//  Created by xiaodong2.zhang on 2025/7/3.
//  Copyright © 2025 ximalaya. All rights reserved.
//

import Foundation

/// Swift 实现的 RN Business 模块，桥接 OC 接口
@objc(RNXMBusiness)
public class RNXMBusiness: RCTEventEmitter {

    // MARK: - RCTBridgeModule 协议实现

    @objc public static func moduleName() -> String {
        return "XMBusiness"
    }

    @objc public static func requiresMainQueueSetup() -> <PERSON><PERSON> {
        return false
    }

    // MARK: - 导出方法

    /// 监听赚取奖励金币（新版）
    /// - Parameters:
    ///   - params: 参数字典，包含 rewardVideoStyle 和 positionName
    ///   - resolve: 成功回调
    ///   - reject: 失败回调
    @objc public func listenEarnRewardCoinNew(_ params: [String: Any],
                                             resolve: @escaping (Any?) -> Void,
                                             reject: @escaping (String, String, Error?) -> Void) {

        // 解析参数
        let rewardVideoStyle = params["rewardVideoStyle"] as? Int ?? 0
        let rewardVideoStyleNew = rewardVideoStyle == 1
        let positionName = params["positionName"] as? String ?? "integral_center_inspire_video"

//        // 调用 ADX 管理器处理激励视频逻辑
//        DispatchQueue.main.async {
//            XMLADXManager.shared().loadRewardVideoAdInfo(params) { result, adItem in
//                // 处理回调结果
//                if let result = result as? [String: Any], !result.isEmpty {
//                    // 成功获取奖励
//                    var responseData: [String: Any] = result
//                    responseData["rewardVideoStyleNew"] = rewardVideoStyleNew
//                    responseData["positionName"] = positionName
//
//                    // 添加广告信息
//                    if let adItem = adItem {
//                        responseData["adInfo"] = self.formatAdItemInfo(adItem)
//                    }
//
//                    resolve(responseData)
//                } else {
//                    // 没有获取到奖励或失败
//                    reject("REWARD_FAILED", "未能获取奖励金币", nil)
//                }
//            }
//        }
    }

    // MARK: - 私有方法

    /// 格式化广告项信息
    /// - Parameter adItem: 广告项
    /// - Returns: 格式化后的广告信息字典
    private func formatAdItemInfo(_ adItem: Any) -> [String: Any] {
        // 这里可以根据实际的广告项类型进行格式化
        // 由于不确定 adItem 的具体类型，这里提供一个基础实现
        var adInfo: [String: Any] = [:]

        // 使用反射获取基本信息
        let mirror = Mirror(reflecting: adItem)
        for child in mirror.children {
            if let label = child.label {
                adInfo[label] = child.value
            }
        }

        return adInfo
    }

    /// 返回常量
    @objc public func constantsToExport() -> [String: Any] {
        return [
            "DEFAULT_POSITION_NAME": "integral_center_inspire_video",
            "REWARD_VIDEO_STYLE_OLD": 0,
            "REWARD_VIDEO_STYLE_NEW": 1
        ]
    }
}
