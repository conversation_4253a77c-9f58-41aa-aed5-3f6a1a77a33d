//
//  RNXMBusinessBridge.m
//  XMADXModule
//
//  Created by xiaodong2.zhang on 2025/7/3.
//  Copyright © 2025 ximalaya. All rights reserved.
//

#import <React/RCTBridgeModule.h>

// 为 Swift RNXMBusiness 模块创建 Objective-C 桥接
@interface RCT_EXTERN_MODULE(RNXMBusiness, NSObject)

// 导出监听赚取奖励金币方法
RCT_EXTERN_METHOD(listenEarnRewardCoinNew:(NSDictionary *)params
                  resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject)

@end
