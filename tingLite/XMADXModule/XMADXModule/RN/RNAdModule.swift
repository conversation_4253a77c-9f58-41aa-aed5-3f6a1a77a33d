//
//  RNAdModule.swift
//  XMADXModule
//
//  Created by xiaodong2.zhang on 2025/7/4.
//  Copyright © 2025 ximalaya. All rights reserved.
//

import Foundation

/// Swift 实现的 RN AdModule 模块
@objc(RNAdModule)
public class RNAdModule: NSObject {

    // MARK: - RCTBridgeModule 协议实现

    @objc public static func moduleName() -> String {
        return "AdModule"
    }

    @objc public static func requiresMainQueueSetup() -> Bool {
        return false
    }

    // MARK: - 导出方法

    /// 点击广告深度链接方法
    /// - Parameters:
    ///   - params: 参数字典
    ///   - resolve: 成功回调
    ///   - reject: 失败回调
    @objc public func clickWithAdDeepLink(_ params: [String: Any],
                                         resolve: @escaping (Any?) -> Void,
                                         reject: @escaping (String, String, Error?) -> Void) {

        print("🎯 Swift clickWithAdDeepLink 被调用了！")
        print("📦 接收到的参数: \(params)")

        // TODO: 在这里实现具体的广告深度链接逻辑

        // 模拟成功响应
        let responseData: [String: Any] = [
            "success": true,
            "message": "AdModule Swift 桥接测试成功！",
            "timestamp": Date().timeIntervalSince1970
        ]

        print("✅ 返回测试数据: \(responseData)")
        resolve(responseData)
    }
}
