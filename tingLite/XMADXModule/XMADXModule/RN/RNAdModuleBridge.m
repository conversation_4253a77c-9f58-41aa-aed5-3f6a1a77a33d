//
//  RNAdModuleBridge.m
//  XMADXModule
//
//  Created by xiaodong2.zhang on 2025/7/4.
//  Copyright © 2025 ximalaya. All rights reserved.
//

#import <React/RCTBridgeModule.h>

// 前向声明 Swift 类
@interface RNAdModule : NSObject
- (void)clickWithAdDeepLink:(NSDictionary *)params 
                    resolve:(void (^)(id))resolve 
                     reject:(void (^)(NSString *, NSString *, NSError *))reject;
@end

@interface RNAdModuleBridge : NSObject <RCTBridgeModule>
@end

@implementation RNAdModuleBridge

RCT_EXPORT_MODULE(AdModule)

RCT_EXPORT_METHOD(clickWithAdDeepLink:(NSDictionary *)params
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    // 调用 Swift 实现
    RNAdModule *swiftModule = [[RNAdModule alloc] init];
    [swiftModule clickWithAdDeepLink:params resolve:resolve reject:reject];
}

@end
