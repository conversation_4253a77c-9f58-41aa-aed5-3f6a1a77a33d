/**
 * AdModule Swift 桥接测试文件
 * 用于验证 AdModule.clickWithAdDeepLink 的 Swift 桥接是否成功
 */

import { NativeModules } from 'react-native';

const { AdModule } = NativeModules;

/**
 * 测试 AdModule Swift 桥接功能
 */
export const testAdModuleBridge = async () => {
  console.log('🚀 开始测试 AdModule Swift 桥接...');
  
  // 检查模块是否存在
  if (!AdModule) {
    console.error('❌ AdModule 模块不存在');
    return false;
  }
  
  console.log('✅ AdModule 模块存在');
  
  // 检查方法是否存在
  if (!AdModule.clickWithAdDeepLink) {
    console.error('❌ clickWithAdDeepLink 方法不存在');
    return false;
  }
  
  console.log('✅ clickWithAdDeepLink 方法存在');
  
  // 测试调用
  try {
    const testParams = {
      deepLink: 'https://example.com/ad/123',
      adId: '12345',
      source: 'test',
      testFlag: true
    };
    
    console.log('📤 发送测试参数:', testParams);
    
    const result = await AdModule.clickWithAdDeepLink(testParams);
    
    console.log('📥 收到返回结果:', result);
    
    // 验证返回结果
    if (result && result.success === true) {
      console.log('🎉 AdModule Swift 桥接测试成功！');
      console.log('📊 测试结果详情:');
      console.log('   - 消息:', result.message);
      console.log('   - 时间戳:', new Date(result.timestamp * 1000).toLocaleString());
      return true;
    } else {
      console.error('❌ 返回结果格式不正确:', result);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 调用失败:', error);
    console.error('错误详情:', {
      code: error.code,
      message: error.message,
      domain: error.domain
    });
    return false;
  }
};

/**
 * 完整的 AdModule 桥接测试
 */
export const runAdModuleBridgeTest = async () => {
  console.log('🧪 开始完整的 AdModule Swift 桥接测试...');
  console.log('='.repeat(50));
  
  const methodResult = await testAdModuleBridge();
  
  console.log('='.repeat(50));
  console.log('📊 AdModule 测试总结:');
  console.log(`   - 方法调用: ${methodResult ? '✅ 成功' : '❌ 失败'}`);
  console.log(`   - 整体结果: ${methodResult ? '🎉 测试成功' : '⚠️ 测试失败'}`);
  
  return methodResult;
};

// 导出测试函数
export default {
  testAdModuleBridge,
  runAdModuleBridgeTest
};
