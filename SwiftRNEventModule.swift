//
//  SwiftRNEventModule.swift
//  tingLite
//
//  Created by x<PERSON>od<PERSON> on 2025/1/14.
//  Copyright © 2025 ximalaya. All rights reserved.
//

import Foundation
import React

/// Swift 实现的 React Native 事件模块示例
@objc(SwiftEventModule)
class SwiftEventModule: RCTEventEmitter {
    
    // MARK: - 单例
    @objc static let shared = SwiftEventModule()
    
    private var hasListeners = false
    
    override init() {
        super.init()
    }
    
    // MARK: - RCTBridgeModule 协议实现
    
    @objc override static func moduleName() -> String {
        return "SwiftEventModule"
    }
    
    @objc override static func requiresMainQueueSetup() -> Bool {
        return true
    }
    
    @objc override func supportedEvents() -> [String] {
        return [
            "onNetworkStatusChanged",
            "onUserLocationChanged",
            "onAppStateChanged",
            "onCustomEvent"
        ]
    }
    
    // MARK: - 监听器管理
    
    @objc override func startObserving() {
        hasListeners = true
        // 开始监听系统事件
        setupNotificationObservers()
    }
    
    @objc override func stopObserving() {
        hasListeners = false
        // 停止监听系统事件
        removeNotificationObservers()
    }
    
    // MARK: - 导出方法
    
    /// 开始监听网络状态
    @objc func startNetworkMonitoring() {
        // 这里可以启动网络监听
        if hasListeners {
            sendEvent(withName: "onNetworkStatusChanged", body: [
                "isConnected": true,
                "connectionType": "wifi",
                "timestamp": Date().timeIntervalSince1970
            ])
        }
    }
    
    /// 发送自定义事件
    @objc func sendCustomEvent(_ params: [String: Any]) {
        if hasListeners {
            sendEvent(withName: "onCustomEvent", body: params)
        }
    }
    
    /// 获取当前位置
    @objc func getCurrentLocation(_ resolve: @escaping RCTPromiseResolveBlock,
                                 reject: @escaping RCTPromiseRejectBlock) {
        
        // 模拟位置获取
        DispatchQueue.global().async {
            Thread.sleep(forTimeInterval: 1.0)
            
            let location = [
                "latitude": 39.9042,
                "longitude": 116.4074,
                "accuracy": 10.0,
                "timestamp": Date().timeIntervalSince1970
            ] as [String : Any]
            
            resolve(location)
            
            // 同时发送位置变化事件
            if self.hasListeners {
                self.sendEvent(withName: "onUserLocationChanged", body: location)
            }
        }
    }
    
    // MARK: - 通知观察者
    
    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
    }
    
    private func removeNotificationObservers() {
        NotificationCenter.default.removeObserver(self)
    }
    
    @objc private func appDidBecomeActive() {
        if hasListeners {
            sendEvent(withName: "onAppStateChanged", body: [
                "state": "active",
                "timestamp": Date().timeIntervalSince1970
            ])
        }
    }
    
    @objc private func appDidEnterBackground() {
        if hasListeners {
            sendEvent(withName: "onAppStateChanged", body: [
                "state": "background",
                "timestamp": Date().timeIntervalSince1970
            ])
        }
    }
    
    // MARK: - 常量导出
    
    @objc override func constantsToExport() -> [String: Any] {
        return [
            "SUPPORTED_EVENTS": supportedEvents(),
            "MODULE_VERSION": "1.0.0"
        ]
    }
}
