//
//  SwiftRNModuleBridge.m
//  tingLite
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/1/14.
//  Copyright © 2025 ximalaya. All rights reserved.
//

#import <React/RCTBridgeModule.h>

// 为 Swift 模块创建 Objective-C 桥接
@interface RCT_EXTERN_MODULE(SwiftBusinessModule, NSObject)

// 导出上传视频方法
RCT_EXTERN_METHOD(uploadVideo:(NSDictionary *)params
                  resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject)

// 导出获取设备信息方法
RCT_EXTERN_METHOD(getDeviceInfo:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject)

// 导出检查应用安装方法
RCT_EXTERN_METHOD(isAppInstalled:(NSDictionary *)params
                  resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject)

// 导出显示原生弹窗方法
RCT_EXTERN_METHOD(showNativeAlert:(NSDictionary *)params
                  resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject)

@end
