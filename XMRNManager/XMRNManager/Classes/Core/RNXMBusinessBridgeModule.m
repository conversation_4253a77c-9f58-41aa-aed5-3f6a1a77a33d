//
//  RNXMBusinessBridgeModule.m
//  XMRNModule
//
//  Created by ocean on 2021/3/8.
//  Copyright © 2021 xmly. All rights reserved.
//

#import "RNXMBusinessBridgeModule.h"
#import <UIKit/UIKit.h>
#import <XMCategories/XMCategory.h>
#import <XMBLKUpload/XMBLKUpload.h>
#import <XMCommonUtil/XMUtility.h>

// 前向声明 XMLADXManager
@interface XMLADXManager : NSObject
+ (instancetype)shared;
- (void)loadRewardVideoAdInfo:(NSDictionary *)params completion:(void(^)(id result, id adItem))completion;
@end

@implementation RNXMBusinessBridgeModule

RCT_EXPORT_MODULE(Business)

RCT_EXPORT_METHOD(uploadVideo:(NSDictionary *)params resolve:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    NSString *filePath = [params safeStringValueForKey:@"filePath"];
    if (filePath.length == 0 || ![NSFileManager.defaultManager fileExistsAtPath:filePath]) {
        return reject(@"-1", @"视频文件不存在", nil);
    }
    NSString *ext = [params safeStringValueForKey:@"ext"];
    if (ext.length == 0) {
        ext = [filePath componentsSeparatedByString:@"."].lastObject;
    }
    NSString *uploadType = [params safeStringValueForKey:@"uploadType"];
    if (uploadType.length == 0) {
        uploadType = @"video";
    }
    
    XMBLKUpload *request = [[XMBLKUpload alloc] initWithFilePath:filePath];
    request.ext = ext;
    request.uploadType = uploadType;
    request.progressBlock = ^(XMBLKUpload *request, CGFloat progress) {
    };
    
    request.successBlock = ^(XMBLKUpload *request) {
        if (request.fileId > 0) {
            resolve(request.fileUrl);
        } else {
            reject(@"-1", @"上传失败，fileid不合法", nil);
        }
    };
    
    request.failureBlock = ^(XMBLKUpload *request) {
        reject(@"-1", [request.requestError description], request.requestError);
    };
    
    [request start];
}

RCT_EXPORT_METHOD(calendarWrite:(NSDictionary *)params
                  resolve:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    
//    [XMUtility writeCalendar:params callback:^(NSDictionary *retDict) {
//        resolve(retDict);
//    }];
}

RCT_EXPORT_METHOD(isAppInstalled:(NSDictionary *)params
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    NSArray *appInfoList = [params arrayMaybeForKey:@"appInfoList"];
    if (!appInfoList) {
        reject(@"-1", @"appInfoList 参数异常", [NSError errorWithDomain:@"xm.rn.business" code:-1 userInfo:@{@"msg": @"appInfoList 参数异常"}]);
        return;
    }
    NSMutableArray *results = [NSMutableArray arrayWithCapacity:appInfoList.count];
    [appInfoList enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (![obj isKindOfClass:[NSString class]]) {
            [results addObject:@(0)];
        } else {
            NSURL *link = [NSURL URLWithString:obj];
            if (link && [[UIApplication sharedApplication] canOpenURL:link]) {
                [results addObject:@(1)];
            } else {
                [results addObject:@(0)];
            }
        }
    }];
    resolve(@{@"result" : results});
}

RCT_EXPORT_METHOD(listenEarnRewardCoinNew:(NSDictionary *)params resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    BOOL rewardVideoStyleNew = [params integerMaybeForKey:@"rewardVideoStyle"] == 1;
    NSString *positionName = [params stringMaybeForKey:@"positionName"]?:@"integral_center_inspire_video";

    // 构建请求参数
    NSMutableDictionary *requestParams = [NSMutableDictionary dictionaryWithDictionary:params];
    requestParams[@"positionName"] = positionName;
    requestParams[@"rewardVideoStyle"] = @(rewardVideoStyleNew ? 1 : 0);

    // 调用 ADX 管理器处理激励视频逻辑
    dispatch_async(dispatch_get_main_queue(), ^{
        [[XMLADXManager shared] loadRewardVideoAdInfo:requestParams completion:^(id result, id adItem) {
            // 处理回调结果
            if (result && [result isKindOfClass:[NSDictionary class]] && [(NSDictionary *)result count] > 0) {
                // 成功获取奖励
                NSMutableDictionary *responseData = [NSMutableDictionary dictionaryWithDictionary:result];
                responseData[@"rewardVideoStyleNew"] = @(rewardVideoStyleNew);
                responseData[@"positionName"] = positionName;

                // 添加广告信息
                if (adItem) {
                    responseData[@"adInfo"] = [self formatAdItemInfo:adItem];
                }

                resolve(responseData);
            } else {
                // 没有获取到奖励或失败
                reject(@"REWARD_FAILED", @"未能获取奖励金币", nil);
            }
        }];
    });
}

// 格式化广告项信息
- (NSDictionary *)formatAdItemInfo:(id)adItem {
    NSMutableDictionary *adInfo = [NSMutableDictionary dictionary];

    // 使用 KVC 获取基本信息
    if ([adItem respondsToSelector:@selector(adid)]) {
        adInfo[@"adId"] = [adItem valueForKey:@"adid"];
    }
    if ([adItem respondsToSelector:@selector(adtype)]) {
        adInfo[@"adType"] = [adItem valueForKey:@"adtype"];
    }
    if ([adItem respondsToSelector:@selector(xml_positionId)]) {
        adInfo[@"positionId"] = [adItem valueForKey:@"xml_positionId"];
    }
    if ([adItem respondsToSelector:@selector(xml_posiName)]) {
        adInfo[@"positionName"] = [adItem valueForKey:@"xml_posiName"];
    }

    return adInfo;
}

@end
