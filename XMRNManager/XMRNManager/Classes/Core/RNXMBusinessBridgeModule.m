//
//  RNXMBusinessBridgeModule.m
//  XMRNModule
//
//  Created by ocean on 2021/3/8.
//  Copyright © 2021 xmly. All rights reserved.
//

#import "RNXMBusinessBridgeModule.h"
#import <UIKit/UIKit.h>
#import <XMCategories/XMCategory.h>
#import <XMBLKUpload/XMBLKUpload.h>
#import <XMCommonUtil/XMUtility.h>

@implementation RNXMBusinessBridgeModule

RCT_EXPORT_MODULE(Business)

RCT_EXPORT_METHOD(uploadVideo:(NSDictionary *)params resolve:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    NSString *filePath = [params safeStringValueForKey:@"filePath"];
    if (filePath.length == 0 || ![NSFileManager.defaultManager fileExistsAtPath:filePath]) {
        return reject(@"-1", @"视频文件不存在", nil);
    }
    NSString *ext = [params safeStringValueForKey:@"ext"];
    if (ext.length == 0) {
        ext = [filePath componentsSeparatedByString:@"."].lastObject;
    }
    NSString *uploadType = [params safeStringValueForKey:@"uploadType"];
    if (uploadType.length == 0) {
        uploadType = @"video";
    }
    
    XMBLKUpload *request = [[XMBLKUpload alloc] initWithFilePath:filePath];
    request.ext = ext;
    request.uploadType = uploadType;
    request.progressBlock = ^(XMBLKUpload *request, CGFloat progress) {
    };
    
    request.successBlock = ^(XMBLKUpload *request) {
        if (request.fileId > 0) {
            resolve(request.fileUrl);
        } else {
            reject(@"-1", @"上传失败，fileid不合法", nil);
        }
    };
    
    request.failureBlock = ^(XMBLKUpload *request) {
        reject(@"-1", [request.requestError description], request.requestError);
    };
    
    [request start];
}

RCT_EXPORT_METHOD(calendarWrite:(NSDictionary *)params
                  resolve:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    
//    [XMUtility writeCalendar:params callback:^(NSDictionary *retDict) {
//        resolve(retDict);
//    }];
}

RCT_EXPORT_METHOD(isAppInstalled:(NSDictionary *)params
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    NSArray *appInfoList = [params arrayMaybeForKey:@"appInfoList"];
    if (!appInfoList) {
        reject(@"-1", @"appInfoList 参数异常", [NSError errorWithDomain:@"xm.rn.business" code:-1 userInfo:@{@"msg": @"appInfoList 参数异常"}]);
        return;
    }
    NSMutableArray *results = [NSMutableArray arrayWithCapacity:appInfoList.count];
    [appInfoList enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (![obj isKindOfClass:[NSString class]]) {
            [results addObject:@(0)];
        } else {
            NSURL *link = [NSURL URLWithString:obj];
            if (link && [[UIApplication sharedApplication] canOpenURL:link]) {
                [results addObject:@(1)];
            } else {
                [results addObject:@(0)];
            }
        }
    }];
    resolve(@{@"result" : results});
}

RCT_EXPORT_METHOD(listenEarnRewardCoinNew:(NSDictionary *)params resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    BOOL rewardVideoStyleNew = [params integerMaybeForKey:@"rewardVideoStyle"] == 1;
    NSString *positionName = [params stringMaybeForKey:@"positionName"]?:@"integral_center_inspire_video";
}

@end
